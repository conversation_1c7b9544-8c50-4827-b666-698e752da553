"""微信认证相关API路由"""

from datetime import datetime, timed<PERSON>ta
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_db
from app.core.config import settings
from app.core.sms_template import SmsTemplate
from app.core.wechat_exceptions import (
    WeChatBindException,
    WeChatErrors,
)
from app.models.user import User
from app.schemas.unified_auth import UnifiedAuthResponse
from app.schemas.wechat import (
    LoginStatusResponse,
    QRCodeResponse,
    WeChatBindRequest,
    WeChatBindResponse,
    WeChatInfoResponse,
    WeChatUnbindResponse,
)
from app.services.auth_service import auth_service
from app.services.logger import get_logger
from app.services.sms_verification import SmsVerification
from app.services.user_service import get_random_nickname
from app.services.validate_phone_number import validate_phone_number
from app.services.wechat_service import QRCodeLoginService, WeChatService

logger = get_logger(__name__)

router = APIRouter()

# 服务实例
wechat_service = WeChatService()
qr_login_service = QRCodeLoginService()
sms_service = SmsVerification()


class WeChatAuthService:
    """微信认证服务类"""

    def __init__(self):
        self.wechat_service = WeChatService()
        self.sms_service = SmsVerification()
        self.logger = get_logger(__name__)
        self.settings = settings

    async def validate_bind_request(self, request: WeChatBindRequest) -> str:
        """验证绑定请求"""
        # 验证手机号格式
        phone = validate_phone_number(request.username)

        # 验证短信验证码
        is_pass = await self.sms_service.verify_code(phone, request.code, SmsTemplate.REGISTER)
        if not is_pass:
            raise HTTPException(status_code=400, detail="验证码错误")

        return phone

    async def get_wechat_user_info(self, openid: str) -> dict:
        """获取微信用户信息"""
        try:
            return await self.wechat_service.get_user_info(openid)
        except Exception as e:
            logger.error(f"获取微信用户信息失败: {e}")
            raise HTTPException(status_code=400, detail="获取微信用户信息失败") from e

    async def check_existing_users(self, db: AsyncSession, username: str, openid: str) -> tuple:
        """检查现有用户情况"""
        # 并行查询提高性能
        user_by_username_query = select(User).where(User.username == username)
        user_by_openid_query = select(User).where(User.wechat_openid == openid)

        user_by_username_result = await db.execute(user_by_username_query)
        user_by_openid_result = await db.execute(user_by_openid_query)

        existing_user_by_username = user_by_username_result.scalar_one_or_none()
        existing_user_by_openid = user_by_openid_result.scalar_one_or_none()

        return existing_user_by_username, existing_user_by_openid

    def validate_bind_conflicts(
        self,
        existing_user_by_username: User,
        existing_user_by_openid: User,
        username: str,
        openid: str,
    ):
        """验证绑定冲突"""
        # 情况1: openid已绑定其他用户名
        if existing_user_by_openid and existing_user_by_openid.username != username:
            raise HTTPException(
                status_code=400,
                detail=f"该微信账号已绑定用户名: {existing_user_by_openid.username}",
            )

        # 情况2: username存在且已绑定其他openid
        if (
            existing_user_by_username
            and existing_user_by_username.wechat_openid
            and existing_user_by_username.wechat_openid != openid
        ):
            raise HTTPException(status_code=400, detail="该用户名已绑定其他微信账号")

    async def process_bind_logic(
        self,
        existing_user_by_username: User,
        existing_user_by_openid: User,
        username: str,
        openid: str,
        wechat_user_info: dict,
        db: AsyncSession,
    ) -> tuple[User, str]:
        """处理绑定逻辑"""
        user = None
        action_message = ""

        # 情况3: username存在但未绑定微信，进行绑定
        if existing_user_by_username and not existing_user_by_username.wechat_openid:
            user = existing_user_by_username
            await self._update_user_wechat_info(user, openid, wechat_user_info)
            user.login_type = "wechat"
            # 更新昵称（如果用户没有设置昵称）
            if not user.nickname:
                user.nickname = wechat_user_info.get("nickname") or await get_random_nickname()
            action_message = "微信绑定成功"

        # 情况4: openid和username都已存在且匹配，更新微信信息
        elif existing_user_by_openid and existing_user_by_openid.username == username:
            user = existing_user_by_openid
            await self._update_user_wechat_info(user, openid, wechat_user_info, update_openid=False)
            action_message = "微信信息更新成功"

        # 情况5: 都不存在，创建新用户
        else:
            user = await self._create_new_user(username, openid, wechat_user_info, db)
            action_message = "注册成功"

        return user, action_message

    async def _update_user_wechat_info(
        self, user: User, openid: str, wechat_user_info: dict, update_openid: bool = True
    ):
        """更新用户微信信息"""
        if update_openid:
            user.wechat_openid = openid
        user.wechat_unionid = wechat_user_info.get("unionid")
        user.wechat_nickname = wechat_user_info.get("nickname")
        user.wechat_avatar = wechat_user_info.get("headimgurl")

    async def _create_new_user(
        self, username: str, openid: str, wechat_user_info: dict, db: AsyncSession
    ) -> User:
        """创建新用户"""
        user = User(
            username=username,
            nickname=wechat_user_info.get("nickname") or await get_random_nickname(),
            wechat_openid=openid,
            wechat_unionid=wechat_user_info.get("unionid"),
            wechat_nickname=wechat_user_info.get("nickname"),
            wechat_avatar=wechat_user_info.get("headimgurl"),
            login_type="wechat",
            is_active=True,
        )
        db.add(user)
        return user

    async def generate_access_token(self, username: str) -> tuple[str, str]:
        """生成访问令牌"""
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await auth_service.create_access_token(
            data={"sub": username}, expires_delta=access_token_expires
        )
        return access_token, "bearer"

    async def get_wechat_info(self, user: User) -> dict[str, Any]:
        """获取用户微信绑定信息"""
        return {
            "is_bound": bool(user.wechat_openid),
            "wechat_nickname": user.wechat_nickname,
            "wechat_avatar": getattr(user, "wechat_avatar", user.avatar),
            "login_type": getattr(user, "login_type", "wechat" if user.wechat_openid else "phone"),
        }

    async def unbind_wechat(self, user: User, db: AsyncSession) -> dict[str, Any]:
        """解绑微信账号"""
        try:
            if not user.wechat_openid:
                raise WeChatErrors.WECHAT_NOT_BOUND

            # 保存原始头像URL（如果不是微信头像）
            original_avatar = user.avatar if not user.wechat_openid else None

            user.wechat_openid = None
            user.wechat_unionid = None
            user.wechat_nickname = None

            # 处理头像字段
            if hasattr(user, "wechat_avatar"):
                user.wechat_avatar = None
            user.avatar = original_avatar

            # 如果有login_type字段，更新登录方式
            if hasattr(user, "login_type"):
                user.login_type = "password"

            # 更新时间戳
            if hasattr(user, "updated_at"):
                user.updated_at = datetime.utcnow()

            await db.commit()

            return {
                "message": "微信账号解绑成功",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "nickname": user.nickname,
                    "wechat_nickname": None,
                    "avatar": user.avatar,
                },
            }

        except WeChatBindException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            self.logger.error(f"微信解绑失败: {str(e)}")
            raise WeChatBindException("微信解绑失败，请稍后重试") from e


# 服务实例
wechat_auth_service = WeChatAuthService()


@router.post("/qr-login/create", response_model=QRCodeResponse, summary="创建登录二维码")
async def create_qr_login():
    """创建微信扫码登录二维码"""
    try:
        qr_info = await qr_login_service.create_login_qr_code()
        logger.info(f"创建登录二维码成功: {qr_info['scene_str']}")
        return QRCodeResponse(**qr_info)
    except Exception as e:
        logger.error(f"创建二维码失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建二维码失败: {str(e)}") from e


@router.get(
    "/qr-login/status/{scene_str}",
    response_model=LoginStatusResponse | UnifiedAuthResponse,
    summary="获取登录状态",
)
async def get_login_status(
    scene_str: str, db: AsyncSession = Depends(get_db)
) -> LoginStatusResponse | UnifiedAuthResponse:
    """获取微信扫码登录状态"""
    try:
        status_info = qr_login_service.get_login_status(scene_str)

        # 如果已确认登录，生成JWT token
        if status_info.get("status") == "confirmed" and status_info.get("openid"):
            openid = status_info["openid"]

            # 查找用户
            result = await db.execute(select(User).where(User.wechat_openid == openid))
            user = result.scalar_one_or_none()

            if user:
                # 更新最后登录时间
                user.last_login = datetime.utcnow()
                await db.commit()
                await db.refresh(user)

                # 生成访问令牌
                access_token, token_type = await wechat_auth_service.generate_access_token(
                    user.username
                )

                logger.info(f"用户 {user.username} 微信扫码登录成功")

                # 直接返回 LoginStatusResponse，其中包含统一的用户信息
                return LoginStatusResponse(
                    status="confirmed",
                    message="登录成功",
                    access_token=access_token,
                    token_type=token_type,
                    user=user,  # 传递完整的 user ORM 对象
                )
            else:
                # 用户不存在，需要注册
                logger.info(f"微信用户 {openid} 需要注册")
                return LoginStatusResponse(
                    status="need_register",
                    message="用户未注册，需要先注册",
                )

        return LoginStatusResponse(
            status=status_info.get("status", "unknown"),
            message=status_info.get("message"),
        )
    except Exception as e:
        logger.error(f"获取登录状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取登录状态失败: {str(e)}") from e


@router.post("/bind", response_model=WeChatBindResponse, summary="微信账号绑定")
async def bind_wechat(
    request: WeChatBindRequest, db: AsyncSession = Depends(get_db)
) -> WeChatBindResponse:
    """微信账号与用户名绑定

    支持以下场景:
    1. 新用户注册（openid和username都不存在）
    2. 现有用户绑定微信（username存在但未绑定微信）
    3. 微信信息更新（openid和username都已存在且匹配）
    4. 冲突检测（防止重复绑定）
    """
    try:
        # 验证请求参数
        await wechat_auth_service.validate_bind_request(request)

        # 获取微信用户信息
        wechat_user_info = await wechat_auth_service.get_wechat_user_info(request.openid)

        # 检查现有用户情况
        (
            existing_user_by_username,
            existing_user_by_openid,
        ) = await wechat_auth_service.check_existing_users(db, request.username, request.openid)

        # 验证绑定冲突
        wechat_auth_service.validate_bind_conflicts(
            existing_user_by_username, existing_user_by_openid, request.username, request.openid
        )

        # 处理绑定逻辑
        user, action_message = await wechat_auth_service.process_bind_logic(
            existing_user_by_username,
            existing_user_by_openid,
            request.username,
            request.openid,
            wechat_user_info,
            db,
        )

        # 提交数据库事务
        await db.commit()
        await db.refresh(user)

        # 生成访问令牌
        access_token, token_type = await wechat_auth_service.generate_access_token(user.username)

        logger.info(f"微信绑定操作成功: {action_message}, 用户: {user.username}")

        # 直接返回 WeChatBindResponse，其中包含统一的用户信息
        return WeChatBindResponse(
            message=action_message,
            user=user,  # 传递完整的 user ORM 对象
            access_token=access_token,
            token_type=token_type,
        )

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"微信绑定操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}") from e


@router.post("/unbind", response_model=WeChatUnbindResponse, summary="解除微信绑定")
async def unbind_wechat(
    current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)
):
    """解除当前用户的微信绑定"""
    try:
        result = await wechat_auth_service.unbind_wechat(current_user, db)
        logger.info(f"用户 {current_user.username} 解除微信绑定成功")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"解除微信绑定失败: {e}")
        raise HTTPException(status_code=500, detail=f"解除绑定失败: {str(e)}") from e


@router.get("/info", response_model=WeChatInfoResponse, summary="获取微信绑定信息")
async def get_wechat_info(current_user: User = Depends(get_current_user)):
    """获取当前用户的微信绑定信息"""
    return await wechat_auth_service.get_wechat_info(current_user)
