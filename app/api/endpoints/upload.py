"""文件上传相关的API接口"""

import asyncio
from enum import Enum
from typing import BinaryIO

from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app import models
from app.api import deps
from app.core.config import get_settings
from app.crud.file_hash import file_hash as crud_file_hash
from app.db.redis import get_redis
from app.db.session import get_db
from app.schemas.file_hash import FileHashCreate
from app.services.logger import get_logger
from app.tasks.image_processing import process_and_upload_image, process_and_upload_video

logger = get_logger(__name__)
settings = get_settings()
router = APIRouter()


class ContentType(str, Enum):
    """上传文件的内容类型"""

    IMAGE = "image"
    VIDEO = "video"


class VideoQuality(str, Enum):
    """视频质量选项"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class UploadResponse(BaseModel):
    """上传响应"""

    file_hash: str
    file_url: str | None = None


async def calculate_file_hash(file_data: bytes | BinaryIO) -> str:
    """计算文件哈希值

    Args:
        file_data: 文件二进制数据或文件对象

    Returns:
        str: 文件哈希值
    """
    import hashlib

    # 使用更大的块大小来处理大文件
    CHUNK_SIZE = 8 * 1024 * 1024  # 8MB chunks
    hash_algorithm = hashlib.sha256()

    if isinstance(file_data, bytes):
        # 分块处理二进制数据
        offset = 0
        while offset < len(file_data):
            chunk = file_data[offset : offset + CHUNK_SIZE]
            hash_algorithm.update(chunk)
            offset += CHUNK_SIZE

            # 每处理64MB数据就让出控制权，避免阻塞事件循环
            if offset % (64 * 1024 * 1024) == 0:
                await asyncio.sleep(0)
    else:
        # 分块读取文件对象
        while chunk := file_data.read(CHUNK_SIZE):
            hash_algorithm.update(chunk)
            # 每处理一个块就让出控制权
            await asyncio.sleep(0)
        # 重置文件指针位置
        file_data.seek(0)

    return hash_algorithm.hexdigest()


@router.post("/", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    content_type: ContentType = Query(...),
    video_quality: VideoQuality = Form(VideoQuality.MEDIUM),
    db: AsyncSession = Depends(get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> UploadResponse:
    """上传文件

    Args:
        file: 文件对象
        content_type: 文件类型，可选值：image, video
        video_quality: 视频质量，可选值：low, medium, high，默认medium

    Returns:
        UploadResponse: 上传响应，包含文件哈希和URL

    Raises:
        HTTPException: 上传失败时抛出异常
    """

    # 计算文件哈希（直接使用文件对象，避免一次性读入内存）
    file_hash = await calculate_file_hash(file.file)

    # 检查Redis缓存
    redis = await get_redis()
    cache_key = f"file_hash:{file_hash}"
    if cached_url := await redis.get(cache_key):
        return UploadResponse(
            file_hash=file_hash,
            file_url=cached_url,
        )

    # Redis没有，查询数据库
    if db_file_hash := await crud_file_hash.get_by_hash(db, file_hash=file_hash):
        # 找到数据库记录，更新Redis缓存
        await redis.set(cache_key, db_file_hash.file_path, ex=settings.FILE_HASH_EXPIRE)
        return UploadResponse(
            file_hash=file_hash,
            file_url=db_file_hash.file_path,
        )

    # 读取文件内容（此时必须读取，因为需要传递给处理任务）
    file_data = await file.read()
    if not file_data:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Empty file")

    # 根据文件类型处理
    if content_type == ContentType.IMAGE:
        # 处理图片
        task = process_and_upload_image.delay(file_data, file_hash)
        file_url = task.get(timeout=30)  # 等待30秒
        if not file_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process image",
            )

    elif content_type == ContentType.VIDEO:
        # 处理视频
        task = process_and_upload_video.delay(file_data, file_hash, video_quality.value)
        file_url = task.get(timeout=300)  # 等待5分钟
        if not file_url:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process video",
            )

    # 同时存储到Redis和数据库
    await redis.set(cache_key, file_url, ex=settings.FILE_HASH_EXPIRE)

    # 创建数据库记录
    file_hash_in = FileHashCreate(file_path=file_url, file_hash=file_hash)
    await crud_file_hash.create(db, obj_in=file_hash_in)

    return UploadResponse(file_hash=file_hash, file_url=file_url)
