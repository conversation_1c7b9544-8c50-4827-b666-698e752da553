"""Token 管理相关的 API 端点"""

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.models.user import User
from app.services.token_service import TokenService

router = APIRouter()


class TokenInfo(BaseModel):
    """Token 信息模型"""

    username: str
    device_id: int | None
    token_type: str
    created_at: str
    expires_at: str


class TokenListResponse(BaseModel):
    """Token 列表响应模型"""

    tokens: list[TokenInfo]
    total: int


class RevokeTokenRequest(BaseModel):
    """撤销 Token 请求模型"""

    device_id: int | None = None


@router.get("/tokens", response_model=TokenListResponse)
async def get_user_tokens(
    current_user: User = Depends(deps.get_current_user),
) -> TokenListResponse:
    """获取当前用户的所有活跃 token"""
    try:
        tokens = await TokenService.get_user_active_tokens(current_user.username)

        token_infos = []
        for token in tokens:
            token_infos.append(
                TokenInfo(
                    username=token.get("username", ""),
                    device_id=token.get("device_id"),
                    token_type=token.get("token_type", "access"),
                    created_at=token.get("created_at", ""),
                    expires_at=token.get("expires_at", ""),
                )
            )

        return TokenListResponse(tokens=token_infos, total=len(token_infos))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取 token 列表失败：{str(e)}")


@router.post("/revoke-token")
async def revoke_token(
    request: RevokeTokenRequest,
    current_user: User = Depends(deps.get_current_user),
) -> dict:
    """撤销指定设备的 token"""
    try:
        if request.device_id:
            # 撤销指定设备的 token
            success = await TokenService.revoke_device_token(
                current_user.username, request.device_id
            )
            if success:
                return {"message": f"设备 {request.device_id} 的 token 已撤销"}
            else:
                raise HTTPException(status_code=404, detail="未找到指定设备的 token")
        else:
            raise HTTPException(status_code=400, detail="请指定要撤销的设备ID")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销 token 失败：{str(e)}")


@router.post("/revoke-all-tokens")
async def revoke_all_tokens(
    current_user: User = Depends(deps.get_current_user),
) -> dict:
    """撤销当前用户的所有 token（除了当前使用的）"""
    try:
        # 注意：这会撤销所有 token，包括当前的，用户需要重新登录
        revoked_count = await TokenService.revoke_user_tokens(current_user.username)

        return {
            "message": f"已撤销 {revoked_count} 个 token，请重新登录",
            "revoked_count": revoked_count,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销所有 token 失败：{str(e)}")


@router.post("/logout")
async def logout(
    current_user: User = Depends(deps.get_current_user),
) -> dict:
    """用户登出（撤销当前 token）"""
    try:
        # 这里需要获取当前的 token，但由于 FastAPI 的限制，我们无法直接获取
        # 作为替代方案，我们撤销用户的所有 token
        revoked_count = await TokenService.revoke_user_tokens(current_user.username)

        return {"message": "登出成功", "revoked_count": revoked_count}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"登出失败：{str(e)}")


# 管理员专用端点
@router.post("/admin/revoke-user-tokens")
async def admin_revoke_user_tokens(
    username: str,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
) -> dict:
    """管理员撤销指定用户的所有 token"""
    try:
        revoked_count = await TokenService.revoke_user_tokens(username)

        return {
            "message": f"已撤销用户 {username} 的所有 token",
            "username": username,
            "revoked_count": revoked_count,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"撤销用户 token 失败：{str(e)}")


@router.get("/admin/user-tokens/{username}", response_model=TokenListResponse)
async def admin_get_user_tokens(
    username: str,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
) -> TokenListResponse:
    """管理员获取指定用户的所有活跃 token"""
    try:
        tokens = await TokenService.get_user_active_tokens(username)

        token_infos = []
        for token in tokens:
            token_infos.append(
                TokenInfo(
                    username=token.get("username", ""),
                    device_id=token.get("device_id"),
                    token_type=token.get("token_type", "access"),
                    created_at=token.get("created_at", ""),
                    expires_at=token.get("expires_at", ""),
                )
            )

        return TokenListResponse(tokens=token_infos, total=len(token_infos))

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户 token 列表失败：{str(e)}")
