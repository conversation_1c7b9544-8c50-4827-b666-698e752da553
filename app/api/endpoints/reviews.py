from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import Action, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.models.review import ContentType, ReviewStatus

router = APIRouter()


@router.post("/", response_model=schemas.Review)
async def create_review(
    *,
    db: AsyncSession = Depends(deps.get_db),
    review_in: schemas.ReviewCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新的审核记录"""
    # 检查内容是否存在
    if review_in.content_type == ContentType.ARTICLE:
        content = await crud.article.get(db, id=review_in.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )
    elif review_in.content_type == ContentType.VIDEO:
        content = await crud.video.get(db, id=review_in.content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在",
            )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的内容类型",
        )

    # 检查是否已存在审核记录
    existing_review = await crud.review.get_by_content(
        db, content_type=review_in.content_type, content_id=review_in.content_id
    )
    if existing_review:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该内容已有审核记录",
        )

    review = await crud.review.create(db=db, obj_in=review_in)
    return review


@router.get("/", response_model=schemas.ReviewList)
async def read_reviews(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: ContentType | None = None,
    status: ReviewStatus | None = None,
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取审核列表"""
    reviews, total = await crud.review.get_multi_with_filter(
        db, skip=skip, limit=limit, content_type=content_type, status=status
    )
    return {"total": total, "items": reviews}


@router.get("/{review_id}", response_model=schemas.Review)
async def read_review(
    *,
    db: AsyncSession = Depends(deps.get_db),
    review_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取审核详情"""
    review = await crud.review.get(db, id=review_id)
    if not review:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="审核记录不存在",
        )
    return review


@router.put("/{review_id}", response_model=schemas.Review)
async def update_review(
    *,
    db: AsyncSession = Depends(deps.get_db),
    review_id: int,
    review_in: schemas.ReviewUpdate,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.SYSTEM, action=Action.MANAGE, scope=Scope.ALL)
        )
    ),
) -> Any:
    """更新审核记录"""
    review = await crud.review.get(db, id=review_id)
    if not review:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="审核记录不存在",
        )

    # 如果状态从待审核变为其他状态，设置审核人和审核时间
    if (
        review.status == ReviewStatus.PENDING
        and review_in.status
        and review_in.status != ReviewStatus.PENDING
    ):
        review_in_dict = review_in.dict(exclude_unset=True)
        if "reviewer_id" not in review_in_dict or not review_in_dict["reviewer_id"]:
            review_in_dict["reviewer_id"] = current_user.id
        review_in = schemas.ReviewUpdate(**review_in_dict)

    review = await crud.review.update(db=db, db_obj=review, obj_in=review_in)

    # 如果审核通过或拒绝，更新内容的发布状态
    if review.status in [ReviewStatus.APPROVED, ReviewStatus.REJECTED]:
        is_published = review.status == ReviewStatus.APPROVED
        if review.content_type == ContentType.ARTICLE:
            article = await crud.article.get(db, id=review.content_id)
            if article:
                await crud.article.update_publish_status(
                    db, db_obj=article, is_published=is_published
                )
        elif review.content_type == ContentType.VIDEO:
            video = await crud.video.get(db, id=review.content_id)
            if video:
                await crud.video.update_publish_status(db, db_obj=video, is_published=is_published)

    return review
