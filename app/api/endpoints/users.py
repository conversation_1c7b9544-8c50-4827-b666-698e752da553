# admin平台用户模型响应模式
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import schemas
from app.api.deps import get_current_user
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import Action, PermissionChecker, ResourceType, Scope
from app.core.permission_system import Permission as PermissionObject
from app.db.session import get_db
from app.models.user import User, UserRole
from app.schemas.unified_auth import UnifiedUserInfo
from app.services.auth_service import AuthService

router = APIRouter()
auth_service = AuthService()


@router.get("/", response_model=list[schemas.UserResponse])
async def get_users(
    db: AsyncSession = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取用户列表"""
    result = await db.execute(select(User).offset(skip).limit(limit))
    users = result.scalars().all()

    # 构建响应 - 使用统一用户信息格式
    result = []
    for user in users:
        user_data = UnifiedUserInfo(
            id=user.id,
            username=user.username,
            nickname=user.nickname,
            email=user.email,
            avatar=getattr(user, "avatar", None),
            is_active=user.is_active,
            description=getattr(user, "description", None),
            last_login=getattr(user, "last_login", None),
            created_at=user.created_at,
            wechat_nickname=getattr(user, "wechat_nickname", None),
            wechat_avatar=getattr(user, "wechat_avatar", None),
            login_type=getattr(user, "login_type", None),
        )
        result.append(user_data)

    return result


@router.post("/", response_model=schemas.UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: schemas.UserCreate,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.CREATE, scope=Scope.ALL)
        )
    ),
) -> Any:
    """创建新用户"""
    # 检查用户名是否已存在
    result = await db.execute(select(User).where(User.username == user_in.username))
    user = result.scalar_one_or_none()
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在",
        )

    # 检查邮箱是否已存在
    if user_in.email:
        result = await db.execute(select(User).where(User.email == user_in.email))
        user = result.scalar_one_or_none()
        if user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 检查角色是否存在
    if user_in.role_id:
        result = await db.execute(select(UserRole).where(UserRole.id == user_in.role_id))
        role = result.scalar_one_or_none()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色不存在",
            )
    else:
        # 使用默认角色
        result = await db.execute(select(UserRole).where(UserRole.is_default))
        role = result.scalar_one_or_none()
        if role:
            user_in.role_id = role.id

    # 创建用户
    user = User(
        username=user_in.username,
        email=user_in.email,
        nickname=user_in.nickname or user_in.username,
        password=auth_service.get_password_hash(user_in.password),
        is_active=user_in.is_active,
        is_superuser=user_in.is_superuser,
        role_id=user_in.role_id,
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)

    # 构建响应 - 使用统一用户信息格式
    user_data = UnifiedUserInfo(
        id=user.id,
        username=user.username,
        nickname=user.nickname,
        email=user.email,
        avatar=getattr(user, "avatar", None),
        is_active=user.is_active,
        last_login=getattr(user, "last_login", None),
        created_at=user.created_at,
        wechat_nickname=getattr(user, "wechat_nickname", None),
        wechat_avatar=getattr(user, "wechat_avatar", None),
        login_type=getattr(user, "login_type", None),
    )

    return user_data


@router.get("/me", response_model=UnifiedUserInfo)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> Any:
    """获取当前用户的详细信息"""
    # 构建响应 - 使用统一用户信息格式
    user_data = UnifiedUserInfo(**current_user.__dict__)

    return user_data


@router.get("/{user_id}", response_model=schemas.UnifiedUserInfo)
async def get_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.READ, scope=Scope.ALL)
        )
    ),
) -> Any:
    """获取特定用户的详细信息"""
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 构建响应 - 使用统一用户信息格式
    user_data = UnifiedUserInfo(**user.__dict__)

    return user_data


@router.put("/{user_id}", response_model=schemas.UnifiedUserInfo)
async def update_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int = Path(..., description="用户 ID（必须为整数）"),
    user_in: schemas.UserUpdate,
    current_user: User = Depends(get_current_user),
) -> Any:
    """更新用户信息"""
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查权限：用户只能更新自己的信息，或者拥有管理权限
    if user.id != current_user.id:
        # 如果不是更新自己的信息，需要检查是否有管理权限
        await PermissionChecker.require_permission(
            db,
            current_user,
            PermissionObject(resource=ResourceType.USER, action=Action.UPDATE, scope=Scope.ALL),
        )
    # 如果是更新自己的信息，直接允许（不需要额外权限检查）

    # 检查是否有权限更新超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权更新超级管理员",
        )

    # 检查邮箱是否已存在
    if user_in.email and user_in.email != user.email:
        result = await db.execute(select(User).where(User.email == user_in.email))
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在",
            )

    # 检查角色是否存在
    if user_in.role_id and user_in.role_id != user.role_id:
        result = await db.execute(select(UserRole).where(UserRole.id == user_in.role_id))
        role = result.scalar_one_or_none()
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色不存在",
            )

    # 更新用户信息 - 使用优雅的部分更新方式
    update_data = user_in.model_dump(exclude_unset=True)
    print("update_data", update_data)

    # 特殊处理密码字段
    if "password" in update_data:
        update_data["password"] = auth_service.get_password_hash(update_data["password"])

    # 批量更新字段
    for field, value in update_data.items():
        setattr(user, field, value)

    await db.commit()
    await db.refresh(user)

    # 构建响应 - 使用统一用户信息格式
    user_data = UnifiedUserInfo(**user.__dict__)

    return user_data


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    *,
    db: AsyncSession = Depends(get_db),
    user_id: int,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.DELETE, scope=Scope.ALL)
        )
    ),
) -> None:
    """删除用户"""
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否有权限删除超级管理员
    if user.is_superuser and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除超级管理员",
        )

    # 不允许删除自己
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户",
        )
    # 删除用户
    await db.delete(user)
    await db.commit()


@router.post("/follow")
async def follow_user(
    *,
    db: AsyncSession = Depends(get_db),
    follow_data: schemas.UserFollow,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.FOLLOW, scope=Scope.ALL)
        )
    ),
) -> Any:
    """关注用户"""
    # 检查被关注用户是否存在
    result = await db.execute(select(User).where(User.id == follow_data.user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否已经关注
    if user in current_user.following:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="已经关注了该用户",
        )

    # 关注用户
    current_user.following.append(user)
    await db.commit()
    await db.refresh(current_user)

    return {"detail": "success"}


@router.post("/unfollow")
async def unfollow_user(
    *,
    db: AsyncSession = Depends(get_db),
    follow_data: schemas.UserFollow,
    current_user: User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.USER, action=Action.FOLLOW, scope=Scope.ALL)
        )
    ),  # Reusing follow permission for now
) -> Any:
    """取消关注用户"""
    # 检查被取消关注的用户是否存在
    result = await db.execute(select(User).where(User.id == follow_data.user_id))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在",
        )

    # 检查是否已经取消关注
    if user not in current_user.following:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="未关注该用户",
        )

    # 取消关注用户
    current_user.following.remove(user)
    await db.commit()
    await db.refresh(current_user)

    return {"detail": "success"}
