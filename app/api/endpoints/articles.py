from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload

from app import crud, models, schemas
from app.api import deps
from app.core.permission_system import Action, Permission, PermissionChecker, ResourceType, Scope
from app.services.article_permission_service import article_permission_service
from app.services.content_service import article_service
from app.services.history_service import record_user_history
from app.services.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.post("/")
async def create_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """创建新文章"""
    # 检查创建权限
    await PermissionChecker.require_permission(
        db,
        current_user,
        Permission(resource=ResourceType.ARTICLE, action=Action.CREATE, scope=Scope.OWN),
    )

    # 用户点击编辑自动为草稿，后续保存皆为更新操作
    article = await crud.article.create(
        db=db,
        obj_in={
            "author_id": current_user.id,
            "title": "未命名文章",  # 设置默认标题
            "content": "",  # 设置空内容
        },
    )
    return article.id


@router.get("/with-stats", response_model=schemas.ArticleListWithStats)
async def read_articles_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取文章列表（包含统计信息）"""
    from app.services.content_stats_service import content_stats_service

    articles = await crud.article.get_multi(db=db, skip=skip, limit=limit)
    total = await db.scalar(select(func.count()).select_from(models.Article))

    # 使用内容统计服务获取统计信息并构建响应
    user_id = current_user.id if current_user else None
    response = await content_stats_service.create_content_list_response(
        db=db, content_type="article", contents=articles, total=total, user_id=user_id
    )

    return response


@router.get("/{category_id}/with-stats", response_model=schemas.ArticleListWithStats)
async def read_articles_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    category_id: int = Path(..., description="分类 ID（必须为整数）"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(10, ge=1, le=10, description="返回的最大记录数"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取包含统计信息的文章列表"""
    from app.services.content_stats_service import content_stats_service

    # 使用权限服务获取可访问的文章
    articles, total = await article_permission_service.get_accessible_articles_by_category(
        db=db, category_id=category_id, current_user=current_user, skip=skip, limit=limit
    )

    # 使用内容统计服务获取统计信息并构建响应
    user_id = current_user.id if current_user else None
    response = await content_stats_service.create_content_list_response(
        db=db, content_type="article", contents=articles, total=total, user_id=user_id
    )

    return response


@router.get("/my", response_model=schemas.ArticleListWithStats)
async def read_my_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: schemas.ArticleStatus = Query(schemas.ArticleStatus.ALL, description="文章状态筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的文章列表（包含统计信息）

    支持的状态筛选：
    - all: 所有文章
    - draft: 草稿（未发布）
    - published_approved: 已发布且已审核通过
    - published_pending: 已发布但待审核
    - published_rejected: 已发布但审核被拒绝
    """
    from app.services.content_stats_service import content_stats_service

    # 使用优化的查询方法，一次获取文章列表和总数
    articles, total = await crud.article.get_multi_by_author_with_status(
        db=db, author_id=current_user.id, status=status, skip=skip, limit=limit
    )

    # 使用内容统计服务获取统计信息并构建响应
    response = await content_stats_service.create_content_list_response(
        db=db, content_type="article", contents=articles, total=total, user_id=current_user.id
    )

    return response


@router.get("/my/with-review", response_model=schemas.ArticleListWithReview)
async def read_my_articles_with_review(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: schemas.ArticleStatus = Query(schemas.ArticleStatus.ALL, description="文章状态筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的文章列表（包含审核信息）

    支持的状态筛选：
    - all: 所有文章
    - draft: 草稿（未发布）
    - published_approved: 已发布且已审核通过
    - published_pending: 已发布但待审核
    - published_rejected: 已发布但审核被拒绝
    
    返回的每篇文章都包含对应的审核信息（如果存在）
    """
    # 使用包含审核信息的查询方法
    articles_with_reviews, total = await crud.article.get_multi_by_author_with_review(
        db=db, author_id=current_user.id, status=status, skip=skip, limit=limit
    )

    # 构建响应数据
    items = []
    for article, review in articles_with_reviews:
        # 创建包含审核信息的文章对象
        article_dict = {
            "id": article.id,
            "title": article.title,
            "description": article.description,
            "cover_url": article.cover_url,
            "author_id": article.author_id,
            "is_published": article.is_published,
            "is_approved": article.is_approved,
            "visit_count": article.visit_count,
            "created_at": article.created_at,
            "updated_at": article.updated_at,
            "category_id": article.category_id,
            "tags": [tag.name for tag in article.tags] if article.tags else [],
            "author": article.author,
            "review": review,  # 包含审核信息
        }
        items.append(article_dict)

    response = schemas.ArticleListWithReview(
        total=total,
        items=items
    )

    return response


@router.get("/{article_id}", response_model=schemas.ArticleDetail)
async def read_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int = Path(..., description="文章 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取文章详情（支持游客访问）"""
    # 获取文章
    article = await crud.article.get(
        db=db,
        id=article_id,
        options=[
            selectinload(models.Article.tags),
            selectinload(models.Article.author),
        ],
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 记录用户历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="article", content_id=article_id
            )
    except Exception as e:
        logger.error(f"记录历史失败: {e}")

    # 检查访问权限
    can_access = await article_permission_service.check_article_access(
        db=db, content=article, current_user=current_user
    )
    if not can_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此文章",
        )

    return article


@router.put("/{article_id}", response_model=schemas.ArticleDetail)
async def update_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新文章
    - 如果文章从未发布变为发布，需要创建审核记录
    - 如果已发布文章的内容有更新，需要重新审核
    - 未发布的文章（草稿）可以自由修改，不需要审核
    """
    # 获取文章
    article = await crud.article.get(
        db=db, id=article_id, options=[joinedload(models.Article.tags)]
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )

    # 检查更新权限
    can_update = await article_permission_service.check_article_update_permission(
        db=db, content=article, current_user=current_user
    )
    if not can_update:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新此文章",
        )

    article_in_dict = article_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in article_in_dict:
        article = await article_service.handle_publish_status(
            db=db, content=article, is_published=article_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(article_in.title or article_in.content)
    article = await article_service.handle_content_update(
        db=db, content=article, has_content_update=has_content_update
    )

    # 处理标签更新
    if article_in.tags is not None:
        article = await article_service.handle_tags_update(
            db=db, content=article, tags=article_in.tags
        )
        # 标签已由服务处理，从字典中移除以避免在基础CRUD中重复处理
        del article_in_dict["tags"]

    # 更新文章
    article = await crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)
    return article


@router.delete("/{article_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """删除文章"""
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )

    # 检查删除权限
    can_delete = await article_permission_service.check_article_delete_permission(
        db=db, content=article, current_user=current_user
    )
    if not can_delete:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此文章",
        )

    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="article", content_id=article.id)
    if review:
        await crud.review.remove(db=db, id=review.id)
    await crud.article.remove(db=db, id=article_id)


@router.get("/{article_id}/with-stats", response_model=schemas.ArticleWithStats)
async def read_article_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int = Path(..., description="文章 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取包含统计信息的文章详情"""
    from app.services.content_stats_service import content_stats_service

    article = await crud.article.get(
        db,
        id=article_id,
        options=[
            selectinload(models.Article.tags),
            selectinload(models.Article.author),
        ],
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )
    # 记录用户浏览历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="article", content_id=article_id
            )
    except Exception as e:
        logger.error(f"记录历史失败: {e}")

    # 检查访问权限
    can_access = await article_permission_service.check_article_access(
        db=db, content=article, current_user=current_user
    )
    if not can_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限访问此文章",
        )

    # 使用 content_stats_service 获取统计信息并构建响应
    enriched_articles = await content_stats_service.enrich_content_with_stats(
        db=db,
        content_type="article",
        contents=[article],
        user_id=current_user.id if current_user else None,
    )

    return enriched_articles[0]
