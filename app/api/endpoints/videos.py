from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy import or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.services.content_service import video_service
from app.services.content_stats_service import ContentStatsService
from app.services.history_service import record_user_history
from app.services.logger import get_logger
from app.services.recommendation_service import RecommendationService
from app.services.video_folder_service import VideoFolderService

# 初始化服务
content_stats_service = ContentStatsService()
recommendation_service = RecommendationService()

logger = get_logger(__name__)

router = APIRouter()


@router.post("/", response_model=schemas.Video)
async def create_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_in: schemas.VideoCreate,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.VIDEO, action=Action.CREATE, scope=Scope.OWN)
        )
    ),
) -> Any:
    """创建新视频
    - 未发布的视频（草稿）不需要审核
    - 发布的视频需要审核
    - 如果未指定folder_id，则使用用户的默认文件夹
    """
    # 处理文件夹ID
    if video_in.folder_id is None:
        # 获取用户的默认文件夹
        default_folder = await VideoFolderService.create_default_folder(db, current_user.id)
        video_in_dict = video_in.dict()
        video_in_dict["folder_id"] = default_folder.id
        video = await crud.video.create(db=db, obj_in=video_in_dict)
    else:
        # 验证文件夹是否存在且属于当前用户
        result = await db.execute(
            select(models.VideoFolder).where(
                models.VideoFolder.id == video_in.folder_id,
                models.VideoFolder.user_id == current_user.id,
                not models.VideoFolder.is_deleted,
            )
        )
        folder = result.scalar_one_or_none()
        if not folder:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="指定的文件夹不存在或不属于当前用户"
            )
        video = await crud.video.create(db=db, obj_in=video_in)

    # 处理发布状态
    video = await video_service.handle_publish_status(
        db=db, content=video, is_published=video.is_published
    )
    return video


@router.get("/", response_model=schemas.VideoList)
async def read_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
    show_draft: bool = False,  # 是否显示草稿
    show_pending: bool = False,  # 是否显示待审核
) -> Any:
    """获取视频列表
    - 管理员：可以看到所有视频
    - 普通用户：
      - 默认只能看到已发布且已审核的视频
      - 可以通过参数查看自己的草稿和待审核视频
    """
    can_read_all = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.VIDEO, action=Action.READ, scope=Scope.ALL),
    )

    if can_read_all:
        # 管理员可以看到所有视频
        videos = await crud.video.get_multi(db, skip=skip, limit=limit)
        result = await db.execute(select(models.Video))
        total = len(result.scalars().all())
    else:
        # 构建查询条件
        conditions = []

        # 已发布且已审核的视频对所有人可见
        conditions.append(models.Video.is_published & models.Video.is_approved)

        # 如果要显示草稿，添加未发布的视频条件
        if show_draft:
            conditions.append(
                ~models.Video.is_published & (models.Video.author_id == current_user.id)
            )

        # 如果要显示待审核，添加已发布未审核的视频条件
        if show_pending:
            conditions.append(
                models.Video.is_published
                & ~models.Video.is_approved
                & (models.Video.author_id == current_user.id)
            )

        # 使用 OR 连接所有条件
        query = select(models.Video).filter(or_(*conditions))

        # 获取分页数据
        result = await db.execute(query.offset(skip).limit(limit))
        videos = result.scalars().all()

        # 获取总数
        count_result = await db.execute(query)
        total = len(count_result.scalars().all())

    return {"total": total, "items": videos}


@router.get("/my", response_model=schemas.VideoListWithStats)
async def read_my_videos(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取当前用户的视频列表（包含统计信息）"""
    from app.services.content_stats_service import content_stats_service

    # 获取用户的视频
    videos = await crud.video.get_multi_by_author(
        db=db, author_id=current_user.id, skip=skip, limit=limit
    )

    # 获取总数
    result = await db.execute(
        select(models.Video).filter(models.Video.author_id == current_user.id)
    )
    total = len(result.scalars().all())

    # 使用内容统计服务获取统计信息并构建响应
    response = await content_stats_service.create_content_list_response(
        db=db, content_type="video", contents=videos, total=total, user_id=current_user.id
    )

    return response


@router.get("/{video_id}", response_model=schemas.Video)
async def read_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取视频详情"""
    video = await video_service.check_permission(db, video_id, current_user)

    # 记录用户历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="video", content_id=video_id
            )
    except Exception as e:
        logger.error(f"记录视频历史失败: {e}")

    return video


@router.put("/{video_id}", response_model=schemas.Video)
async def update_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    video_in: schemas.VideoUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新视频
    - 如果视频从未发布变为发布，需要创建审核记录
    - 如果已发布视频的内容有更新，需要重新审核
    - 未发布的视频（草稿）可以自由修改，不需要审核
    """
    # 检查更新权限
    video = await video_service.check_update_permission(db, video_id, current_user)

    video_in_dict = video_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in video_in_dict:
        video = await video_service.handle_publish_status(
            db=db, content=video, is_published=video_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(video_in.title or video_in.description or video_in.url)
    video = await video_service.handle_content_update(
        db=db, content=video, has_content_update=has_content_update
    )

    # 更新视频
    video = await crud.video.update(db=db, db_obj=video, obj_in=video_in_dict)
    return video


@router.put("/{video_id}/move", response_model=schemas.Video)
async def move_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID（必须为整数）"),
    folder_id: int = Query(..., description="目标文件夹 ID（必须为整数）"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """移动视频到指定文件夹"""
    # 检查移动权限
    video = await video_service.check_move_permission(
        db=db, video_id=video_id, folder_id=folder_id, current_user=current_user
    )
    # 移动视频
    video = await crud.video.update(
        db=db,
        db_obj=video,
        obj_in={"folder_id": folder_id},
    )
    return video


@router.delete("/{video_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_video(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """删除视频"""
    # 检查删除权限
    video = await video_service.check_delete_permission(db, video_id, current_user)
    # 删除视频及其关联数据
    await video_service.delete_content(db, video)


@router.get("/{category_id}/with-stats", response_model=schemas.VideoListWithStats)
async def read_videos_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    category_id: int = Path(..., description="分类 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取包含统计信息的视频列表"""
    from app.services.content_stats_service import content_stats_service

    # 构建查询条件
    conditions = []

    # 已发布且已审核的视频对所有人可见
    conditions.append(models.Video.is_published & models.Video.is_approved)
    conditions.append(models.Video.category_id == category_id)

    # 使用 OR 连接所有条件
    query = select(models.Video).filter(or_(*conditions))

    # 获取分页数据
    result = await db.execute(query.offset(skip).limit(limit))
    videos = result.scalars().all()

    # 获取总数
    count_result = await db.execute(query)
    total = len(count_result.scalars().all())

    # 使用内容统计服务获取统计信息并构建响应
    user_id = current_user.id if current_user else None
    response = await content_stats_service.create_content_list_response(
        db=db, content_type="video", contents=videos, total=total, user_id=user_id
    )

    return response


@router.get("/{video_id}/with-stats", response_model=schemas.VideoWithStats)
async def read_video_with_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """获取包含统计信息的视频详情"""
    from app.services.content_stats_service import content_stats_service

    # 检查权限并获取视频
    video = await video_service.check_permission(db, video_id, current_user)
    if not video:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在",
        )

    # 记录用户浏览历史（访问次数已由中间件处理）
    try:
        if current_user:
            # 如果用户已登录，记录历史
            await record_user_history(
                db=db, user_id=current_user.id, content_type="video", content_id=video_id
            )
    except Exception as e:
        logger.error(f"记录视频历史失败: {e}")

    # 使用内容统计服务获取统计信息
    user_id = current_user.id if current_user else None
    videos_with_stats = await content_stats_service.enrich_content_with_stats(
        db=db, content_type="video", contents=[video], user_id=user_id
    )

    return videos_with_stats[0]


@router.get("/{video_id}/play-list", response_model=schemas.VideoList)
async def get_play_list(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int = Path(..., description="视频 ID（必须为整数）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
    limit: int = Query(10, ge=1, le=20, description="播放列表视频数量"),
    list_type: str = Query(
        "folder",
        description="播放列表类型：folder-同文件夹视频, similar-相似视频, recommend-推荐视频",
    ),
) -> Any:
    """获取视频播放列表
    - folder: 获取当前视频所在文件夹的其他视频
    - similar: 获取与当前视频相似的视频
    - recommend: 获取推荐视频
    """
    # 1. 获取并验证视频
    video = await video_service.check_permission(db, video_id, current_user)
    if not video:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="视频不存在或无权访问")

    # 2. 根据类型获取视频列表
    user_id = current_user.id if current_user else None
    videos = []
    logger.info(f"获取视频播放列表，video_folder_id={video.folder_id}")
    if list_type == "folder":
        if video.folder_id:
            videos = await VideoFolderService.get_videos_in_same_folder(
                db, video_id=video.id, folder_id=video.folder_id, limit=limit
            )
    elif list_type == "similar":
        videos = await recommendation_service.get_similar_videos(
            db, video_id=video.id, limit=limit, user_id=user_id
        )
    elif list_type == "recommend":
        videos = await recommendation_service.get_recommended_videos(
            db, user_id=user_id, limit=limit, exclude_video_id=video.id
        )

    # 3. 丰富视频统计信息
    if videos:
        videos = await content_stats_service.enrich_content_with_stats(
            db=db, content_type="video", contents=videos, user_id=user_id
        )

    return schemas.VideoList(total=len(videos), items=videos)
