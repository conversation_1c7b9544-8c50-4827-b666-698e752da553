from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api import deps
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import (
    Action,
    PermissionChecker,
    ResourceType,
    Scope,
)
from app.core.permission_system import (
    Permission as PermissionObject,
)
from app.models.comment import CommentType
from app.schemas.comment import (
    Comment,
    CommentCreate,
    CommentList,
    CommentUpdate,
    CommentWithReplies,
    Reply,
)

router = APIRouter()


@router.post("/", response_model=Comment, status_code=status.HTTP_201_CREATED)
async def create_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_in: CommentCreate,
    current_user: models.User = Depends(
        PermissionDeps.require_permission_enhanced(
            PermissionObject(resource=ResourceType.COMMENT, action=Action.CREATE, scope=Scope.OWN)
        )
    ),
) -> Any:
    """创建评论或回复"""
    # 验证回复的父评论
    if comment_in.parent_id:
        parent_comment = await crud.comment.get(db, id=comment_in.parent_id)
        if not parent_comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="回复的父评论不存在",
            )
        # 确保回复与父评论属于同一文章/视频
        if (
            parent_comment.article_id != comment_in.article_id
            or parent_comment.video_id != comment_in.video_id
        ):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="回复必须与父评论属于同一文章或视频",
            )
        # 设置reply_to_id为被回复的评论ID
        comment_in.reply_to_id = comment_in.parent_id

    # 验证评论类型和关联ID
    if comment_in.comment_type == CommentType.ARTICLE:
        if not comment_in.article_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文章评论必须提供article_id",
            )
        # 检查文章是否存在且已发布
        article = await crud.article.get(db, id=comment_in.article_id)
        if not article or not article.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在或未发布",
            )
    elif comment_in.comment_type == CommentType.VIDEO:
        if not comment_in.video_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="视频评论必须提供video_id",
            )
        # 检查视频是否存在且已发布
        video = await crud.video.get(db, id=comment_in.video_id)
        if not video or not video.is_published:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="视频不存在或未发布",
            )

    # 创建评论
    comment = await crud.comment.create(db, obj_in=comment_in, author_id=current_user.id)

    return comment


@router.get("/article/{article_id}", response_model=CommentList)
async def get_article_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取文章评论列表"""
    # 检查文章是否存在且已发布
    article = await crud.article.get(db, id=article_id)
    if not article or not article.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在或未发布",
        )

    # 获取评论列表
    comments_orm = await crud.comment.get_by_article(
        db, article_id=article_id, skip=skip, limit=limit
    )
    total = await crud.comment.count_by_article(db, article_id=article_id)

    # 手动将ORM模型转换为Pydantic模型以避免懒加载问题
    comment_map = {}
    # 预先计算每个顶层评论的回复数量
    reply_counts = {}
    for comment_orm in comments_orm:
        if comment_orm.parent_id:
            reply_counts[comment_orm.parent_id] = reply_counts.get(comment_orm.parent_id, 0) + 1

    for comment_orm in comments_orm:
        if not comment_orm.parent_id:
            # 这是一个顶层评论
            # 使用字典转换避免直接访问ORM对象的懒加载属性
            comment_dict = {
                "id": comment_orm.id,
                "content": comment_orm.content,
                "comment_type": comment_orm.comment_type,
                "article_id": comment_orm.article_id,
                "video_id": comment_orm.video_id,
                "parent_id": comment_orm.parent_id,
                "is_visible": comment_orm.is_visible,
                "author_id": comment_orm.author_id,
                "author": comment_orm.author,
                "created_at": comment_orm.created_at,
                "updated_at": comment_orm.updated_at,
                # 不从ORM对象获取replies
            }
            pydantic_comment = CommentWithReplies.model_validate(comment_dict)
            pydantic_comment.replies = []  # 明确初始化空列表
            pydantic_comment.reply_count = reply_counts.get(comment_orm.id, 0)  # 设置回复数量
            comment_map[pydantic_comment.comment_id] = pydantic_comment

    for comment_orm in comments_orm:
        if comment_orm.parent_id:
            parent_comment = comment_map.get(comment_orm.parent_id)
            if parent_comment:
                # 这是一个回复，同样使用字典转换
                reply_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "parent_id": comment_orm.parent_id,
                    "reply_to_id": comment_orm.reply_to_id,  # 添加 reply_to_id 字段
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    # 添加回复目标用户信息
                    "reply_to_user_id": parent_comment.author_id,
                    "reply_to_user": parent_comment.author,
                }
                pydantic_reply = Reply.model_validate(reply_dict)
                parent_comment.replies.append(pydantic_reply)

    # 对回复按时间排序
    for comment in comment_map.values():
        comment.replies.sort(key=lambda x: x.created_at)

    return {"total": total, "items": list(comment_map.values())}


@router.get("/video/{video_id}", response_model=CommentList)
async def get_video_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    video_id: int,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取视频评论列表"""
    # 检查视频是否存在且已发布
    video = await crud.video.get(db, id=video_id)
    if not video or not video.is_published:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="视频不存在或未发布",
        )

    # 获取评论列表
    comments_orm = await crud.comment.get_by_video(db, video_id=video_id, skip=skip, limit=limit)
    total = await crud.comment.count_by_video(db, video_id=video_id)

    # 手动将ORM模型转换为Pydantic模型以避免懒加载问题
    comment_map = {}
    # 预先计算每个顶层评论的回复数量
    reply_counts = {}
    for comment_orm in comments_orm:
        if comment_orm.parent_id:
            reply_counts[comment_orm.parent_id] = reply_counts.get(comment_orm.parent_id, 0) + 1

    for comment_orm in comments_orm:
        if not comment_orm.parent_id:
            # 这是一个顶层评论
            # 使用字典转换避免直接访问ORM对象的懒加载属性
            comment_dict = {
                "id": comment_orm.id,
                "content": comment_orm.content,
                "comment_type": comment_orm.comment_type,
                "article_id": comment_orm.article_id,
                "video_id": comment_orm.video_id,
                "parent_id": comment_orm.parent_id,
                "is_visible": comment_orm.is_visible,
                "author_id": comment_orm.author_id,
                "author": comment_orm.author,
                "created_at": comment_orm.created_at,
                "updated_at": comment_orm.updated_at,
                # 不从ORM对象获取replies
            }
            pydantic_comment = CommentWithReplies.model_validate(comment_dict)
            pydantic_comment.replies = []  # 明确初始化空列表
            pydantic_comment.reply_count = reply_counts.get(comment_orm.id, 0)  # 设置回复数量
            comment_map[pydantic_comment.comment_id] = pydantic_comment

    for comment_orm in comments_orm:
        if comment_orm.parent_id:
            parent_comment = comment_map.get(comment_orm.parent_id)
            if parent_comment:
                # 这是一个回复，同样使用字典转换
                reply_dict = {
                    "id": comment_orm.id,
                    "content": comment_orm.content,
                    "comment_type": comment_orm.comment_type,
                    "article_id": comment_orm.article_id,
                    "video_id": comment_orm.video_id,
                    "parent_id": comment_orm.parent_id,
                    "reply_to_id": comment_orm.reply_to_id,  # 添加 reply_to_id 字段
                    "is_visible": comment_orm.is_visible,
                    "author_id": comment_orm.author_id,
                    "author": comment_orm.author,
                    "created_at": comment_orm.created_at,
                    "updated_at": comment_orm.updated_at,
                    # 添加回复目标用户信息
                    "reply_to_user_id": parent_comment.author_id,
                    "reply_to_user": parent_comment.author,
                }
                pydantic_reply = Reply.model_validate(reply_dict)
                parent_comment.replies.append(pydantic_reply)

    # 对回复按时间排序
    for comment in comment_map.values():
        comment.replies.sort(key=lambda x: x.created_at)

    return {"total": total, "items": list(comment_map.values())}


@router.get("/user/me", response_model=list[Comment])
async def get_my_comments(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """获取当前用户的评论列表"""
    comments = await crud.comment.get_by_user(db, author_id=current_user.id, skip=skip, limit=limit)
    return comments


@router.put("/{comment_id}", response_model=Comment)
async def update_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    comment_in: CommentUpdate,
    current_user: models.User = Depends(deps.get_current_user),
) -> Any:
    """更新评论"""
    # 获取评论
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    can_update_own = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.UPDATE, scope=Scope.OWN),
    )
    can_manage = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not ((comment.author_id == current_user.id and can_update_own) or can_manage):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新此评论",
        )

    # 更新评论
    comment = await crud.comment.update(db, db_obj=comment, obj_in=comment_in)
    return comment


@router.delete("/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_comment(
    *,
    db: AsyncSession = Depends(deps.get_db),
    comment_id: int,
    current_user: models.User = Depends(deps.get_current_user),
) -> None:
    """删除评论"""
    # 获取评论
    comment = await crud.comment.get(db, id=comment_id)
    if not comment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="评论不存在",
        )

    # 检查权限
    can_delete_own = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.DELETE, scope=Scope.OWN),
    )
    can_manage = await PermissionChecker.check_permission(
        db,
        current_user,
        PermissionObject(resource=ResourceType.COMMENT, action=Action.MANAGE, scope=Scope.ALL),
    )
    if not ((comment.author_id == current_user.id and can_delete_own) or can_manage):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此评论",
        )

    # 删除评论
    await crud.comment.remove(db, id=comment_id)
