"""微信公众号消息推送和扫码登录"""

import hashlib
import xml.etree.ElementTree as ET

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import HTMLResponse
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db
from app.core.config import get_settings
from app.models.user import User
from app.services.wechat_service import qr_login_service, wechat_service

settings = get_settings()

router = APIRouter()


def verify_wechat_signature(signature: str, timestamp: str, nonce: str) -> bool:
    """
    验证微信签名

    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数

    Returns:
        bool: 验证是否通过
    """
    # 获取微信Token
    token = settings.WECHAT_MESSAGE_TOKEN

    # 将Token、timestamp、nonce三个参数进行字典序排序
    params = [token, timestamp, nonce]
    params.sort()

    # 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = "".join(params)
    sha1_hash = hashlib.sha1(temp_str.encode("utf-8")).hexdigest()

    # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    return sha1_hash == signature


def parse_wechat_xml(xml_data: str) -> dict:
    """解析微信XML消息"""
    try:
        root = ET.fromstring(xml_data)
        result = {}
        for child in root:
            result[child.tag] = child.text
        return result
    except ET.ParseError:
        return {}


def create_text_response(to_user: str, from_user: str, content: str) -> str:
    """创建文本回复消息的XML格式"""
    import time

    create_time = int(time.time())
    xml_template = f"""<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{create_time}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
    return xml_template


@router.get("/")
async def receive_wechat_message(
    request: Request, signature: str, timestamp: str, nonce: str, echostr: str
) -> str:
    """
    接收微信公众号推送的消息

    微信服务器在向第三方服务器发起请求时，会在请求的query string中携带以下参数：
    - signature: 微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数
    - timestamp: 时间戳
    - nonce: 随机数
    - echostr: 随机字符串

    开发者通过检验signature对请求进行校验（下面有校验方式）。
    若确认此次GET请求来自微信服务器，请原样返回echostr参数内容，则接入生效，成为开发者成功，否则接入失败。

    加密/校验流程如下：
    1. 将token、timestamp、nonce三个参数进行字典序排序
    2. 将三个参数字符串拼接成一个字符串进行sha1加密
    3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    """
    # 验证签名
    if not verify_wechat_signature(signature, timestamp, nonce):
        raise HTTPException(status_code=403, detail="签名验证失败")

    # 验证通过，返回echostr
    return HTMLResponse(content=echostr)


@router.post("/")
async def handle_wechat_event(
    request: Request,
    signature: str,
    timestamp: str,
    nonce: str,
    db: AsyncSession = Depends(get_db),
):
    """处理微信事件消息"""
    # 验证签名
    if not verify_wechat_signature(signature, timestamp, nonce):
        raise HTTPException(status_code=403, detail="签名验证失败")

    # 获取XML数据
    xml_data = await request.body()
    xml_str = xml_data.decode("utf-8")

    # 解析XML消息
    msg_data = parse_wechat_xml(xml_str)
    print("msg_data", msg_data)

    if not msg_data:
        return "success"

    msg_type = msg_data.get("MsgType")
    event = msg_data.get("Event")
    from_user = msg_data.get("FromUserName")
    to_user = msg_data.get("ToUserName")

    # 处理扫码事件
    if msg_type == "event" and event == "SCAN":
        response_message = await handle_scan_event(msg_data, db)
        if response_message:
            return create_text_response(from_user, to_user, response_message)
    elif msg_type == "event" and event == "subscribe":
        # 处理关注事件（可能包含扫码）
        response_message = await handle_subscribe_event(msg_data, db)
        if response_message:
            return create_text_response(from_user, to_user, response_message)

    return "success"


async def handle_scan_event(msg_data: dict, db: AsyncSession) -> str:
    """处理扫码事件"""
    openid = msg_data.get("FromUserName")
    event_key = msg_data.get("EventKey")

    if not openid or not event_key:
        return None

    # 检查是否是登录二维码
    if event_key.startswith("login_"):
        scene_str = event_key

        # 获取用户信息
        try:
            user_info = await wechat_service.get_user_info(openid)

            # 更新登录状态
            qr_login_service.update_login_status(
                scene_str, "confirmed", openid=openid, user_info=user_info
            )

            # 检查用户是否已注册
            result = await db.execute(select(User).where(User.wechat_openid == openid))
            user = result.scalar_one_or_none()

            if user:
                return f"🎉 登录成功！\n\n欢迎回来，{user.nickname or user.username}！\n您已成功登录到系统。"
            else:
                return "✅ 扫码成功！\n\n您的微信账号尚未绑定，请在网页端完成注册流程。"

        except Exception:
            # 如果获取用户信息失败，仍然标记为已扫码
            qr_login_service.update_login_status(scene_str, "scanned", openid=openid)
            return "✅ 扫码成功！\n\n正在处理您的登录请求，请稍候..."

    return None


async def handle_subscribe_event(msg_data: dict, db: AsyncSession) -> str:
    """处理关注事件"""
    openid = msg_data.get("FromUserName")
    event_key = msg_data.get("EventKey")

    if not openid:
        return None

    # 如果有EventKey，说明是通过扫码关注的
    if event_key and event_key.startswith("qrscene_login_"):
        # 提取scene_str
        scene_str = event_key.replace("qrscene_", "")

        # 获取用户信息
        try:
            user_info = await wechat_service.get_user_info(openid)

            # 更新登录状态
            qr_login_service.update_login_status(
                scene_str, "confirmed", openid=openid, user_info=user_info
            )

            # 检查用户是否已注册
            result = await db.execute(select(User).where(User.wechat_openid == openid))
            user = result.scalar_one_or_none()

            if user:
                return f"🎉 关注成功，登录完成！\n\n欢迎回来，{user.nickname or user.username}！\n感谢您关注我们的公众号，您已成功登录到系统。"
            else:
                return "🎉 关注成功！\n\n感谢您关注我们的公众号！\n您的微信账号尚未绑定，请在网页端完成注册流程。"

        except Exception:
            # 如果获取用户信息失败，仍然标记为已扫码
            qr_login_service.update_login_status(scene_str, "scanned", openid=openid)
            return "🎉 关注成功！\n\n感谢您关注我们的公众号！\n正在处理您的登录请求，请稍候..."
    else:
        # 普通关注事件
        return "🎉 关注成功！\n\n感谢您关注我们的公众号！\n如需登录系统，请扫描网页上的二维码。"

    return None
