from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.db.session import get_db
from app.models.user import User, UserRole

settings = get_settings()

# OAuth2密码流的令牌URL
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

# 可选的OAuth2密码流（用于支持游客访问）
oauth2_scheme_optional = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False
)


class TokenData(BaseModel):
    username: str | None = None
    permissions: list[str] = []


async def get_current_user(
    db: AsyncSession = Depends(get_db), token: str = Depends(oauth2_scheme)
) -> User:
    """获取当前用户"""
    from app.services.token_service import TokenService

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # 使用 TokenService 验证 token
    token_info = await TokenService.verify_token(token)
    if not token_info:
        raise credentials_exception

    username = token_info.get("sub")
    print("username", username)
    if not username:
        raise credentials_exception

    # 查询用户（预加载 role 和 role.permissions 关系）
    from sqlalchemy.orm import selectinload

    result = await db.execute(
        select(User)
        .options(selectinload(User.role).selectinload(UserRole.permissions))
        .where(User.username == username)
    )
    user = result.scalar_one_or_none()
    if user is None:
        raise credentials_exception
    if not user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")

    return user


async def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前激活用户"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户未激活")
    return current_user


async def get_current_active_superuser(
    current_user: Annotated[User, Depends(get_current_user)],
) -> User:
    """获取当前超级管理员用户"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="用户权限不足")
    return current_user


async def get_current_user_optional(
    db: AsyncSession = Depends(get_db), token: str | None = Depends(oauth2_scheme_optional)
) -> User | None:
    """
    获取当前用户（可选）
    用于支持游客访问的端点

    Args:
        db: 数据库会话
        token: 可选的访问令牌

    Returns:
        用户对象或None（游客）
    """
    if not token:
        return None

    try:
        return await get_current_user(db=db, token=token)
    except HTTPException:
        # 如果token无效，返回None（游客）
        return None
