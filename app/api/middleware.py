import json
from typing import Any

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.logger import logger


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """统一异常处理中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        try:
            response = await call_next(request)
            return response
        except HTTPException as http_exc:
            # 记录客户端错误（4xx）的详细信息，用于调试
            if 400 <= http_exc.status_code < 500:
                logger.info(
                    f"客户端错误 {http_exc.status_code}: {http_exc.detail} "
                    f"- {request.method} {request.url.path}"
                )
            # 直接返回HTTPException，不进行转换
            return JSONResponse(
                status_code=http_exc.status_code, content={"detail": http_exc.detail}
            )
        except Exception as e:
            # 记录真正的服务器错误
            logger.error(
                f"未处理的异常: {str(e)} - {request.method} {request.url.path}", exc_info=True
            )
            # 只有真正的未处理异常才返回500
            return JSONResponse(status_code=500, content={"detail": "内部服务器错误"})


class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    """重写全局响应格式化中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        response = await call_next(request)

        # 对于OPTIONS请求（CORS预检）和204状态码，直接返回原始响应
        if request.method == "OPTIONS" or response.status_code == 204:
            return response

        if (
            request.url.path.startswith("/api/v1")
            and response.status_code < 400
            and response.status_code != 204
            and request.url.path not in ["/api/v1/health"]
        ):
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                content = json.loads(body.decode("utf-8")) if body else {}
                formatted_content = {"status": "success", "data": content}
                response_body = json.dumps(
                    formatted_content, ensure_ascii=False, default=str
                ).encode("utf-8")
                # 创建新的headers字典，排除Content-Length让FastAPI自动计算
                # 确保保留所有重要的头部信息，包括CORS头部
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content=response_body,
                    status_code=response.status_code,
                    headers=new_headers,
                    media_type=response.media_type or "application/json",
                )
            except Exception:
                # 异常情况下也要排除Content-Length头部
                new_headers = {
                    k: v for k, v in response.headers.items() if k.lower() != "content-length"
                }
                return Response(
                    content="Internal Server Error",
                    status_code=500,
                    headers=new_headers,
                    media_type=response.media_type,
                )
        return response
