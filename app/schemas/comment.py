from datetime import datetime

from pydantic import BaseModel, Field

from app.models.comment import CommentType
from app.schemas.user import User


class CommentBase(BaseModel):
    """评论基础模型"""

    content: str = Field(..., description="评论内容")
    comment_type: CommentType = Field(..., description="评论类型：article或video")
    article_id: int | None = Field(None, description="文章ID，当comment_type为article时必填")
    video_id: int | None = Field(None, description="视频ID，当comment_type为video时必填")
    parent_id: int | None = Field(None, description="父评论ID，用于回复")
    is_visible: bool | None = Field(True, description="是否可见")


class CommentCreate(CommentBase):
    """创建评论模型"""

    reply_to_id: int | None = Field(None, description="回复目标评论ID")


class CommentUpdate(BaseModel):
    """更新评论模型"""

    content: str | None = Field(None, description="评论内容")
    is_visible: bool | None = Field(None, description="是否可见")


class CommentInDBBase(CommentBase):
    """数据库中的评论模型"""

    id: int
    author_id: int
    author: User | None = None
    reply_to_id: int | None = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Comment(CommentInDBBase):
    """返回给API的评论模型"""

    comment_id: int = Field(..., alias="id")
    author: User | None = None

    class Config:
        from_attributes = True
        populate_by_name = True


class Reply(Comment):
    """返回给API的回复模型"""

    reply_id: int = Field(..., alias="id")
    reply_to_comment_id: int | None = Field(..., alias="parent_id")
    reply_to_user_id: int | None = None  # 添加回复目标用户ID
    reply_to_user: User | None = None    # 添加回复目标用户信息

    class Config:
        from_attributes = True
        populate_by_name = True


class CommentWithReplies(Comment):
    """包含回复的评论模型"""

    replies: list[Reply] = []
    reply_count: int = 0  # 添加回复数量字段


class CommentList(BaseModel):
    """评论列表模型"""

    total: int
    items: list[CommentWithReplies]
