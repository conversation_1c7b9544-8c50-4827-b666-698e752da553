from datetime import datetime
from enum import Enum

from pydantic import BaseModel, field_serializer

from app.schemas.tag import TagBase
from app.schemas.user import User
from app.schemas.review import Review


class ArticleStatus(str, Enum):
    """文章状态枚举"""
    ALL = "all"  # 所有文章
    DRAFT = "draft"  # 草稿（未发布）
    PUBLISHED_APPROVED = "published_approved"  # 已发布且已审核通过
    PUBLISHED_PENDING = "published_pending"  # 已发布但待审核
    PUBLISHED_REJECTED = "published_rejected"  # 已发布但审核被拒绝


class ArticleBase(BaseModel):
    """文章基础模型"""

    title: str | None = None  # 标题
    content: str | None = None  # 内容
    description: str | None = None  # 描述
    cover_url: str | None = None  # 封面图URL
    is_published: bool = False  # 是否发布（草稿状态为False）
    is_approved: bool = False  # 是否通过审核
    tags: list[TagBase] | None = None  # 标签列表


class ArticleCreate(BaseModel):
    """创建文章的请求模型"""

    author_id: int
    title: str | None = None  # 可选标题，如果不提供则使用默认值
    content: str | None = None  # 可选内容，如果不提供则使用默认值


class ArticleUpdate(BaseModel):
    """更新文章的请求模型"""

    title: str | None = None
    content: str | None = None
    description: str | None = None
    cover_url: str | None = None
    is_published: bool | None = None
    tags: list[str] | None = None
    category_id: int | None = None


class ArticleInDBBase(ArticleBase):
    """数据库中文章的基础模型"""

    id: int
    author_id: int
    category_id: int | None = None
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class Article(ArticleInDBBase):
    """API响应中的文章模型（不包含content字段，用于列表展示）"""

    author: User
    # category: Category | None = None

    model_config = {"exclude": {"content"}, "from_attributes": True}

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleDetail(ArticleInDBBase):
    """文章详情模型（包含content字段，用于详情页面展示）"""

    author: User

    model_config = {"from_attributes": True}

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleWithStats(ArticleInDBBase):
    """包含统计信息的文章模型（不包含content字段，用于列表展示）"""

    author: User
    like_count: int = 0
    favorite_count: int = 0
    visit_count: int = 0  # 访问次数
    is_liked_by_user: bool = False
    is_favorited_by_user: bool = False

    model_config = {"exclude": {"content"}, "from_attributes": True}

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleList(BaseModel):
    """文章列表响应模型（不包含content字段）"""

    total: int
    items: list[Article]


class ArticleWithReview(ArticleInDBBase):
    """包含审核信息的文章模型（不包含content字段，用于列表展示）"""

    author: User
    review: Review | None = None  # 审核信息

    model_config = {"exclude": {"content"}, "from_attributes": True}

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleWithStatsAndReview(ArticleInDBBase):
    """包含统计信息和审核信息的文章模型（不包含content字段，用于列表展示）"""

    author: User
    like_count: int = 0
    favorite_count: int = 0
    visit_count: int = 0  # 访问次数
    is_liked_by_user: bool = False
    is_favorited_by_user: bool = False
    review: Review | None = None  # 审核信息

    model_config = {"exclude": {"content"}, "from_attributes": True}

    @field_serializer("tags")
    def serialize_tags(self, tags: list[TagBase] | None) -> list[str] | None:
        """将标签对象列表序列化为字符串列表"""
        if tags is None:
            return None
        return [tag.name for tag in tags]


class ArticleListWithStats(BaseModel):
    """包含统计信息的文章列表响应模型（不包含content字段）"""

    total: int
    items: list[ArticleWithStats]


class ArticleListWithReview(BaseModel):
    """包含审核信息的文章列表响应模型（不包含content字段）"""

    total: int
    items: list[ArticleWithReview]


class ArticleListWithStatsAndReview(BaseModel):
    """包含统计信息和审核信息的文章列表响应模型（不包含content字段）"""

    total: int
    items: list[ArticleWithStatsAndReview]
