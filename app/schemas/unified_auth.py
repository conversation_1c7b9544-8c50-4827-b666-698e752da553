"""统一认证响应模型

用于统一SMS认证和微信认证的返回值结构
"""

from datetime import datetime
from typing import Any

from pydantic import BaseModel, Field, field_validator

from app.core.config import get_settings


class UnifiedUserInfo(BaseModel):
    """统一用户信息模型"""

    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    nickname: str | None = Field(None, description="昵称")
    email: str | None = Field(None, description="邮箱")
    avatar: str | None = Field(None, description="头像URL")
    cover: str | None = Field(None, description="封面")
    is_active: bool = Field(..., description="是否激活")
    description: str | None = Field(None, description="描述")
    last_login: datetime | None = Field(None, description="最后登录时间")
    created_at: datetime = Field(..., description="创建时间")
    # 微信相关字段
    wechat_nickname: str | None = Field(None, description="微信昵称")
    wechat_avatar: str | None = Field(None, description="微信头像")
    login_type: str | None = Field(None, description="登录方式")

    @field_validator("wechat_avatar", mode="before")
    @classmethod
    def add_cdn_domain_wechat_avatar(cls, v: str | None) -> str | None:
        """为微信头像URL自动添加CDN域名前缀"""
        if not v:
            return v

        # 微信头像通常已经是完整URL，直接返回
        if v.startswith(("http://", "https://")):
            return v

        # 如果是相对路径，添加CDN域名前缀
        settings = get_settings()
        cdn_domain = settings.OSS_CDN_DOMAIN.rstrip("/")

        # 确保路径以/开头
        if not v.startswith("/"):
            v = "/" + v

        return f"{cdn_domain}{v}"

    class Config:
        from_attributes = True


class UnifiedAuthResponse(BaseModel):
    """统一认证响应模型"""

    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    user: UnifiedUserInfo = Field(..., description="用户信息")
    message: str | None = Field(None, description="操作结果消息")

    # 额外的认证信息
    auth_method: str = Field(..., description="认证方式: sms, wechat")

    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "user": {
                    "id": 1,
                    "username": "13800138000",
                    "nickname": "用户昵称",
                    "email": "<EMAIL>",
                    "avatar": "https://example.com/avatar.jpg",
                    "is_active": True,
                    "last_login": "2024-01-01T12:00:00",
                    "created_at": "2024-01-01T10:00:00",
                    "wechat_nickname": "微信昵称",
                    "wechat_avatar": "https://wx.qlogo.cn/...",
                    "login_type": "wechat",
                },
                "message": "登录成功",
                "auth_method": "wechat",
            }
        }


class UnifiedDeviceVerificationResponse(BaseModel):
    """统一设备验证响应模型"""

    requires_device_verification: bool = Field(default=True, description="需要设备验证")
    message: str = Field(..., description="验证消息")
    device_info: dict[str, Any] | None = Field(None, description="设备信息")
    verification_token: str | None = Field(None, description="验证令牌")
    auth_method: str = Field(..., description="认证方式: sms, wechat")

    class Config:
        json_schema_extra = {
            "example": {
                "requires_device_verification": True,
                "message": "检测到新设备登录，需要进行设备验证",
                "device_info": {"device_type": "mobile", "os": "iOS", "browser": "Safari"},
                "verification_token": "device_verify_token_123",
                "auth_method": "sms",
            }
        }


class UnifiedAuthResult:
    """统一认证结果封装类

    用于内部服务之间传递认证结果
    """

    def __init__(
        self,
        success: bool,
        access_token: str | None = None,
        user: Any | None = None,
        requires_device_verification: bool = False,
        verification_token: str | None = None,
        device_info: dict[str, Any] | None = None,
        message: str | None = None,
        auth_method: str = "unknown",
    ):
        self.success = success
        self.access_token = access_token
        self.user = user
        self.requires_device_verification = requires_device_verification
        self.verification_token = verification_token
        self.device_info = device_info
        self.message = message
        self.auth_method = auth_method

    def to_auth_response(self) -> UnifiedAuthResponse:
        """转换为统一认证响应"""
        if not self.success or not self.access_token or not self.user:
            raise ValueError("认证失败，无法生成响应")

        return UnifiedAuthResponse(
            access_token=self.access_token,
            token_type="bearer",
            user=UnifiedUserInfo.model_validate(self.user),
            message=self.message or "登录成功",
            auth_method=self.auth_method,
        )

    def to_device_verification_response(self) -> UnifiedDeviceVerificationResponse:
        """转换为设备验证响应"""
        if not self.requires_device_verification:
            raise ValueError("不需要设备验证，无法生成设备验证响应")

        return UnifiedDeviceVerificationResponse(
            requires_device_verification=True,
            message=self.message or "需要设备验证",
            device_info=self.device_info,
            verification_token=self.verification_token,
            auth_method=self.auth_method,
        )
