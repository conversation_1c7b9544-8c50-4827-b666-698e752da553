from datetime import datetime

from pydantic import BaseModel


class TagBase(BaseModel):
    """标签基础模型"""

    name: str
    
    model_config = {
        "from_attributes": True
    }


class TagCreate(TagBase):
    """创建标签的请求模型"""

    pass


class TagUpdate(TagBase):
    """更新标签的请求模型"""

    name: str | None = None


class Tag(TagBase):
    """标签的响应模型"""

    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }
