from datetime import datetime

from pydantic import BaseModel


class CategoryBase(BaseModel):
    """分类基础模型"""

    name: str
    description: str | None = None
    parent_id: int | None = None


class CategoryCreate(CategoryBase):
    """创建分类的请求模型"""

    pass


class CategoryUpdate(CategoryBase):
    """更新分类的请求模型"""

    pass


class CategoryInDBBase(CategoryBase):
    """数据库中分类的基础模型"""

    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Category(CategoryInDBBase):
    """API响应中的分类模型"""

    children: list["Category"] = []


# 更新前向引用
Category.model_rebuild()
