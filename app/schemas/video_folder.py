from datetime import datetime

from pydantic import BaseModel, Field


class VideoFolderBase(BaseModel):
    """视频文件夹基础模型"""

    name: str = Field(..., description="文件夹名称")
    path: str = Field(..., description="文件夹路径，以/开头，如/favorites")
    is_public: bool | None = Field(False, description="是否公开可见")
    access_level: int | None = Field(
        0, description="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见"
    )
    cover_url: str | None = Field(None, description="文件夹封面图URL")
    sort_order: int | None = Field(0, description="排序权重，值越大越靠前")


class VideoFolderCreate(VideoFolderBase):
    """创建视频文件夹请求模型"""

    pass


class VideoFolderUpdate(BaseModel):
    """更新视频文件夹请求模型"""

    name: str | None = Field(None, description="文件夹名称")
    is_public: bool | None = Field(None, description="是否公开可见")
    access_level: int | None = Field(
        None, description="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见"
    )
    cover_url: str | None = Field(None, description="文件夹封面图URL")
    sort_order: int | None = Field(None, description="排序权重，值越大越靠前")


class VideoFolderInDB(VideoFolderBase):
    """数据库中的视频文件夹模型"""

    id: int
    user_id: int
    parent_id: int | None = None
    is_default: bool = False
    is_deleted: bool = False
    created_at: datetime
    updated_at: datetime
    deleted_at: datetime | None = None
    video_count: int = Field(0, description="文件夹中的视频数量")

    class Config:
        from_attributes = True


class VideoFolder(VideoFolderInDB):
    """API响应的视频文件夹模型"""

    # video_count 已经从数据库模型中继承
    has_children: bool | None = Field(False, description="是否有子文件夹")


class VideoFolderTree(VideoFolder):
    """视频文件夹树形结构模型"""

    children: list["VideoFolderTree"] = Field(default_factory=list, description="子文件夹列表")
