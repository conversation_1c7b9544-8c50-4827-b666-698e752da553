from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, Field


class UserBrowseHistoryBase(BaseModel):
    """用户浏览历史基础模型"""
    content_type: Literal["article", "video"]
    content_id: int
    duration: int | None = None
    source: str | None = None
    device_type: str | None = None
    ip_address: str | None = None
    user_agent: str | None = None


class UserBrowseHistoryCreate(UserBrowseHistoryBase):
    """创建用户浏览历史的请求模型"""
    pass


class UserBrowseHistory(UserBrowseHistoryBase):
    """用户浏览历史响应模型"""
    id: int
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class UserInteractionBase(BaseModel):
    """用户交互行为基础模型"""
    content_type: Literal["article", "video"]
    content_id: int
    interaction_type: str = Field(..., description="交互类型：view, like, favorite, comment, share, click等")
    weight: float = Field(default=1.0, description="交互权重")
    extra_data: str | None = None


class UserInteractionCreate(UserInteractionBase):
    """创建用户交互行为的请求模型"""
    pass


class UserInteraction(UserInteractionBase):
    """用户交互行为响应模型"""
    id: int
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True


class UserProfileBase(BaseModel):
    """用户画像基础模型"""
    interest_tags: str | None = None
    preferred_categories: str | None = None
    active_hours: str | None = None
    preferred_device: str | None = None
    content_preference: str = Field(default="both", description="内容偏好：article, video, both")
    diversity_preference: float = Field(default=0.5, ge=0, le=1, description="多样性偏好")


class UserProfileCreate(UserProfileBase):
    """创建用户画像的请求模型"""
    pass


class UserProfileUpdate(BaseModel):
    """更新用户画像的请求模型"""
    interest_tags: str | None = None
    preferred_categories: str | None = None
    active_hours: str | None = None
    preferred_device: str | None = None
    content_preference: str | None = None
    diversity_preference: float | None = Field(None, ge=0, le=1)


class UserProfile(UserProfileBase):
    """用户画像响应模型"""
    id: int
    user_id: int
    last_updated: datetime
    created_at: datetime

    class Config:
        from_attributes = True


class RecommendationItem(BaseModel):
    """推荐内容项模型"""
    content_type: Literal["article", "video"]
    content_id: int
    score: float = Field(..., description="推荐分数")
    reason: str | None = Field(None, description="推荐原因")


class RecommendationRequest(BaseModel):
    """推荐请求模型"""
    algorithm_type: str | None = Field(None, description="指定推荐算法类型")
    position: str | None = Field(None, description="推荐位置")
    limit: int = Field(default=10, ge=1, le=50, description="推荐数量")
    content_type: Literal["article", "video"] | None = Field(None, description="内容类型过滤")
    exclude_seen: bool = Field(default=True, description="是否排除已浏览内容")


class RecommendationResponse(BaseModel):
    """推荐响应模型"""
    items: list[RecommendationItem]
    algorithm_type: str
    total_count: int
    generated_at: datetime


class RecommendationLogBase(BaseModel):
    """推荐记录基础模型"""
    algorithm_type: str
    recommended_items: str = Field(..., description="推荐内容JSON")
    recommendation_reason: str | None = None
    position: str | None = None


class RecommendationLogCreate(RecommendationLogBase):
    """创建推荐记录的请求模型"""
    pass


class RecommendationLog(RecommendationLogBase):
    """推荐记录响应模型"""
    id: int
    user_id: int
    is_clicked: bool
    clicked_content_id: int | None
    clicked_at: datetime | None
    created_at: datetime

    class Config:
        from_attributes = True


class ContentSimilarityBase(BaseModel):
    """内容相似度基础模型"""
    source_content_type: Literal["article", "video"]
    source_content_id: int
    target_content_type: Literal["article", "video"]
    target_content_id: int
    similarity_score: float = Field(..., ge=0, le=1, description="相似度分数")
    similarity_type: str = Field(..., description="相似度类型")


class ContentSimilarityCreate(ContentSimilarityBase):
    """创建内容相似度的请求模型"""
    pass


class ContentSimilarity(ContentSimilarityBase):
    """内容相似度响应模型"""
    id: int
    calculated_at: datetime

    class Config:
        from_attributes = True


class RecommendationStats(BaseModel):
    """推荐统计模型"""
    total_recommendations: int
    click_through_rate: float
    algorithm_performance: dict[str, Any]
    popular_content_types: dict[str, int]


class UserInterestProfile(BaseModel):
    """用户兴趣画像模型"""
    user_id: int
    interest_tags: dict[str, float] = Field(..., description="兴趣标签及权重")
    preferred_categories: dict[str, float] = Field(..., description="偏好分类及权重")
    content_preference: str
    activity_score: float = Field(..., description="活跃度分数")
    last_active: datetime


class SimilarContentResponse(BaseModel):
    """相似内容响应模型"""
    content_type: Literal["article", "video"]
    content_id: int
    similar_items: list[RecommendationItem]
    similarity_type: str


class HotContentResponse(BaseModel):
    """热门内容响应模型"""
    content_type: Literal["article", "video"]
    items: list[dict[str, Any]]
    time_range: str = Field(..., description="时间范围：today, week, month")
    generated_at: datetime


class RecommendationFeedback(BaseModel):
    """推荐反馈模型"""
    recommendation_log_id: int
    feedback_type: Literal["like", "dislike", "not_interested", "report"]
    content_id: int | None = None
    reason: str | None = None
