# 使schemas目录成为一个Python包
from app.schemas.article import (
    Article,
    ArticleCreate,
    ArticleDetail,
    ArticleList,
    ArticleListWithStats,
    ArticleListWithReview,
    ArticleListWithStatsAndReview,
    ArticleStatus,
    ArticleUpdate,
    ArticleWithStats,
    ArticleWithReview,
    ArticleWithStatsAndReview,
)
from app.schemas.comment import Comment, CommentCreate, CommentList, CommentUpdate
from app.schemas.favorite import (
    ContentFavoriteInfo,
    Favorite,
    FavoriteBase,
    FavoriteCreate,
    FavoriteHistory,
    FavoriteStats,
    FavoriteStatus,
    FavoriteToggle,
    FavoriteUpdate,
    FavoriteWithContent,
)
from app.schemas.file_hash import FileHash, FileHashBase, FileHashCreate, FileHashUpdate
from app.schemas.history import (
    History,
    HistoryCreate,
    HistoryList,
    HistoryWithContent,
)
from app.schemas.like import (
    ContentLikeInfo,
    Like,
    LikeBase,
    LikeCreate,
    LikeHistory,
    LikeStats,
    LikeStatus,
    LikeToggle,
)
from app.schemas.recommendation import (
    ContentSimilarity,
    ContentSimilarityCreate,
    HotContentResponse,
    RecommendationFeedback,
    RecommendationItem,
    RecommendationLog,
    RecommendationLogCreate,
    RecommendationRequest,
    RecommendationResponse,
    RecommendationStats,
    SimilarContentResponse,
    UserBrowseHistory,
    UserBrowseHistoryCreate,
    UserInteraction,
    UserInteractionCreate,
    UserInterestProfile,
    UserProfile,
    UserProfileCreate,
    UserProfileUpdate,
)
from app.schemas.review import Review, ReviewCreate, ReviewList, ReviewUpdate
from app.schemas.unified_auth import (
    UnifiedAuthResponse,
    UnifiedAuthResult,
    UnifiedDeviceVerificationResponse,
    UnifiedUserInfo,
)
from app.schemas.user import (
    UserBase,
    UserCreate,
    UserFollow,
    UserInDB,
    UserInDBBase,
    UserResponse,
    UserUpdate,
)
from app.schemas.video import (
    Video,
    VideoCreate,
    VideoList,
    VideoListWithStats,
    VideoUpdate,
    VideoWithStats,
)
from app.schemas.wechat import (
    LoginStatusResponse,
    QRCodeResponse,
    WeChatBindRequest,
    WeChatBindResponse,
    WeChatInfoResponse,
    WeChatLoginRequest,
    WeChatMessageEvent,
    WeChatUnbindResponse,
    WeChatUserInfo,
)

# 在这里导入所有模式，以便在其他地方可以通过app.schemas导入
