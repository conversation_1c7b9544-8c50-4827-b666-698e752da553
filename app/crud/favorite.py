from datetime import datetime
from typing import Any

from sqlalchemy import and_, func, or_, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.favorite import Favorite
from app.schemas.favorite import FavoriteCreate, FavoriteUpdate


class CRUDFavorite(CRUDBase[Favorite, FavoriteCreate, FavoriteUpdate]):
    """收藏CRUD操作"""

    async def get_by_user_and_content(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> Favorite | None:
        """根据用户和内容获取收藏记录"""
        result = await db.execute(
            select(self.model).where(
                and_(
                    self.model.user_id == user_id,
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )
        )
        return result.scalar_one_or_none()

    async def toggle_favorite(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str,
        content_id: int,
        note: str | None = None,
    ) -> tuple[Favorite, bool]:
        """切换收藏状态"""
        existing_favorite = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )

        if existing_favorite:
            existing_favorite.is_active = not existing_favorite.is_active
            existing_favorite.updated_at = datetime.utcnow()
            if note is not None:
                existing_favorite.note = note
            db.add(existing_favorite)
            await db.commit()
            await db.refresh(existing_favorite)
            return existing_favorite, existing_favorite.is_active
        else:
            favorite_data = {
                "user_id": user_id,
                "content_type": content_type,
                "content_id": content_id,
                "note": note,
                "is_active": True,
            }
            new_favorite = self.model(**favorite_data)
            db.add(new_favorite)
            await db.commit()
            await db.refresh(new_favorite)
            return new_favorite, True

    async def get_content_favorite_count(
        self, db: AsyncSession, *, content_type: str, content_id: int
    ) -> int:
        """获取内容的点赞数量"""
        result = await db.execute(
            select(func.count(self.model.id)).where(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                    self.model.is_active,
                )
            )
        )
        return result.scalar() or 0

    async def get_user_favorites(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str | None = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[Favorite]:
        """获取用户的点赞历史"""
        query = select(self.model).where(and_(self.model.user_id == user_id, self.model.is_active))

        if content_type:
            query = query.where(self.model.content_type == content_type)

        query = query.order_by(self.model.created_at.desc()).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_user_favorite_count(
        self, db: AsyncSession, *, user_id: int, content_type: str | None = None
    ) -> int:
        """获取用户的点赞总数"""
        query = select(func.count(self.model.id)).where(
            and_(self.model.user_id == user_id, self.model.is_active)
        )

        if content_type:
            query = query.where(self.model.content_type == content_type)

        result = await db.execute(query)
        return result.scalar() or 0

    async def is_favorited_by_user(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> bool:
        """检查用户是否已点赞"""
        favorite = await self.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        return favorite is not None and favorite.is_active

    async def get_content_favorites_batch(
        self, db: AsyncSession, *, content_items: list[tuple[str, int]], user_id: int | None = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取内容的收藏信息

        Args:
            content_items: [(content_type, content_id), ...] 列表
            user_id: 可选的用户ID，用于检查用户是否已收藏

        Returns:
            {(content_type, content_id): {"favorite_count": int, "is_favorited": bool}}
        """
        if not content_items:
            return {}

        # 构建查询条件
        conditions = []
        for content_type, content_id in content_items:
            conditions.append(
                and_(
                    self.model.content_type == content_type,
                    self.model.content_id == content_id,
                )
            )

        # 获取收藏统计
        favorite_counts_result = await db.execute(
            select(
                self.model.content_type,
                self.model.content_id,
                func.count(self.model.id).label("favorite_count"),
            )
            .where(
                and_(
                    self.model.is_active,
                    or_(*conditions),
                )
            )
            .group_by(self.model.content_type, self.model.content_id)
        )
        favorite_counts = favorite_counts_result.all()

        # 构建结果字典
        result = {}
        for content_type, content_id, count in favorite_counts:
            result[(content_type, content_id)] = {"favorite_count": count}

        # 如果提供了用户ID，检查用户收藏状态
        if user_id:
            user_favorites_result = await db.execute(
                select(self.model.content_type, self.model.content_id).where(
                    and_(
                        self.model.user_id == user_id,
                        self.model.is_active,
                        or_(*conditions),
                    )
                )
            )
            user_favorites = user_favorites_result.all()

            for content_type, content_id in user_favorites:
                if (content_type, content_id) in result:
                    result[(content_type, content_id)]["is_favorited"] = True

        return result


favorite = CRUDFavorite(Favorite)
