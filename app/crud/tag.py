from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.tag import Tag
from app.schemas.tag import TagCreate, TagUpdate


class CRUDTag(CRUDBase[Tag, TagCreate, TagUpdate]):
    """标签的 CRUD 操作"""

    async def get_by_name(self, db: AsyncSession, *, name: str) -> Tag | None:
        """根据标签名获取标签

        Args:
            db: 数据库会话
            name: 标签名

        Returns:
            标签对象，如果不存在则返回 None
        """
        result = await db.execute(select(self.model).where(self.model.name == name))
        return result.scalar_one_or_none()

    async def get_or_create(self, db: AsyncSession, *, name: str) -> Tag:
        """获取标签，如果不存在则创建

        Args:
            db: 数据库会话
            name: 标签名

        Returns:
            标签对象
        """
        tag = await self.get_by_name(db, name=name)
        if not tag:
            tag = await self.create(db, obj_in=TagCreate(name=name))
        return tag

    async def get_or_create_multi(self, db: AsyncSession, *, names: list[str]) -> list[Tag]:
        """获取多个标签，不存在的标签会被创建

        Args:
            db: 数据库会话
            names: 标签名列表

        Returns:
            标签对象列表
        """
        tags = []
        for name in names:
            tag = await self.get_or_create(db, name=name)
            tags.append(tag)
        return tags


tag = CRUDTag(Tag)
