from typing import Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.video import Video
from app.schemas.video import VideoCreate, VideoUpdate
from app.services.folder_stats_service import folder_stats_service


class CRUDVideo(CRUDBase[Video, VideoCreate, VideoUpdate]):
    async def get_by_title(self, db: AsyncSession, *, title: str) -> Video | None:
        """根据标题获取视频"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.author_id == author_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取已发布的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.is_published).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_by_folder(
        self, db: AsyncSession, *, folder_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定文件夹下的视频列表"""
        result = await db.execute(
            select(self.model).where(self.model.folder_id == folder_id).offset(skip).limit(limit)
        )
        return result.scalars().all()
        
    async def get_multi_by_ids(
        self, db: AsyncSession, *, ids: list[int]
    ) -> list[Video]:
        """根据ID列表获取多个视频"""
        if not ids:
            return []
        result = await db.execute(
            select(self.model).where(
                self.model.id.in_(ids),
                self.model.is_published == True,
                self.model.is_approved == True,
                self.model.is_deleted == False
            )
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Video, is_published: bool
    ) -> Video:
        """更新视频发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_pending_review(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的待审核视频列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            待审核视频列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(self.model.is_published & ~self.model.is_approved)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_drafts(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Video]:
        """获取指定作者的草稿列表

        Args:
            db: 数据库会话
            author_id: 作者ID
            skip: 跳过的记录数
            limit: 返回的最大记录数

        Returns:
            草稿列表
        """
        result = await db.execute(
            select(self.model)
            .where(self.model.author_id == author_id)
            .where(~self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: VideoCreate) -> Video:
        """创建视频，并更新文件夹视频数量

        Args:
            db: 数据库会话
            obj_in: 创建视频的数据

        Returns:
            创建的视频
        """
        # 调用父类的创建方法
        db_obj = await super().create(db, obj_in=obj_in)

        # 更新文件夹视频数量
        if db_obj.folder_id:
            await folder_stats_service.update_folder_video_count(db, db_obj.folder_id)

        return db_obj

    async def update(
        self,
        db: AsyncSession,
        *,
        db_obj: Video,
        obj_in: VideoUpdate | dict[str, Any],
    ) -> Video:
        """更新视频，如果文件夹变更则更新相关文件夹的视频数量

        Args:
            db: 数据库会话
            db_obj: 数据库中的视频对象
            obj_in: 更新的数据

        Returns:
            更新后的视频
        """
        # 记录原始文件夹ID
        old_folder_id = db_obj.folder_id

        # 调用父类的更新方法
        updated_obj = await super().update(db, db_obj=db_obj, obj_in=obj_in)

        # 如果文件夹ID变更，更新两个文件夹的视频数量
        if (
            isinstance(obj_in, dict)
            and "folder_id" in obj_in
            and old_folder_id != updated_obj.folder_id
            or hasattr(obj_in, "folder_id")
            and obj_in.folder_id is not None
            and old_folder_id != updated_obj.folder_id
        ):
            if old_folder_id:
                await folder_stats_service.update_folder_video_count(db, old_folder_id)
            if updated_obj.folder_id:
                await folder_stats_service.update_folder_video_count(db, updated_obj.folder_id)

        return updated_obj

    async def remove(self, db: AsyncSession, *, id: int) -> Video:
        """删除视频，并更新文件夹视频数量

        Args:
            db: 数据库会话
            id: 视频ID

        Returns:
            删除的视频
        """
        # 先获取视频对象，记录文件夹ID
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalar_one_or_none()
        if not obj:
            raise ValueError(f"视频ID {id} 不存在")

        folder_id = obj.folder_id

        # 调用父类的删除方法
        deleted_obj = await super().remove(db, id=id)

        # 更新文件夹视频数量
        if folder_id:
            await folder_stats_service.update_folder_video_count(db, folder_id)

        return deleted_obj

    async def move_to_folder(
        self, db: AsyncSession, *, db_obj: Video, folder_id: int | None
    ) -> Video:
        """移动视频到指定文件夹，并更新相关文件夹的视频数量

        Args:
            db: 数据库会话
            db_obj: 视频对象
            folder_id: 目标文件夹ID

        Returns:
            更新后的视频
        """
        # 记录原始文件夹ID
        old_folder_id = db_obj.folder_id

        # 更新文件夹ID
        db_obj.folder_id = folder_id
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)

        # 更新两个文件夹的视频数量
        if old_folder_id:
            await folder_stats_service.update_folder_video_count(db, old_folder_id)
        if folder_id:
            await folder_stats_service.update_folder_video_count(db, folder_id)

        return db_obj


video = CRUDVideo(Video)
