from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.video_folder import VideoFolder


class CRUDVideoFolder(CRUDBase[VideoFolder, dict, dict]):
    async def get_by_path(self, db: AsyncSession, *, user_id: int, path: str) -> VideoFolder | None:
        """根据路径获取文件夹"""
        result = await db.execute(
            select(self.model).filter(
                self.model.user_id == user_id,
                self.model.path == path
            )
        )
        return result.scalar_one_or_none()

    async def get_by_user(self, db: AsyncSession, *, user_id: int) -> list[VideoFolder]:
        """获取用户的所有文件夹"""
        result = await db.execute(
            select(self.model).filter(self.model.user_id == user_id)
        )
        return result.scalars().all()

    async def get_children(self, db: AsyncSession, *, user_id: int, parent_path: str) -> list[VideoFolder]:
        """获取指定路径下的所有子文件夹"""
        if parent_path == "/":
            # 根目录特殊处理
            result = await db.execute(
                select(self.model).filter(
                    self.model.user_id == user_id,
                    self.model.path.like("/%"),
                    ~self.model.path.like("/%/%"),  # 排除子文件夹
                )
            )
            return result.scalars().all()
        else:
            # 确保parent_path以/结尾
            if not parent_path.endswith("/"):
                parent_path += "/"
            result = await db.execute(
                select(self.model).filter(
                    self.model.user_id == user_id,
                    self.model.path.like(f"{parent_path}%"),
                    ~self.model.path.like(f"{parent_path}%/%"),  # 排除子文件夹
                )
            )
            return result.scalars().all()


video_folder = CRUDVideoFolder(VideoFolder)
