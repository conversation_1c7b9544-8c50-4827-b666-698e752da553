from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.base import CRUDBase
from app.models.user import User


class CRUDUser(CRUDBase[User, None, None]):
    async def get_by_username(self, db: AsyncSession, *, username: str) -> User | None:
        """通过用户名获取用户"""
        query = select(self.model).filter(self.model.username == username)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def is_admin(self, user: User | None) -> bool:
        """检查用户是否为管理员

        Args:
            user: 用户对象

        Returns:
            bool: 是否为管理员
        """
        if not user:
            return False
        return user.is_superuser


user = CRUDUser(User)
