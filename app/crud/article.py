from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.article import Article
from app.models.review import ContentType, Review
from app.models.user import User
from app.schemas.article import ArticleCreate, ArticleStatus, ArticleUpdate


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
    async def get_by_title(self, db: AsyncSession, *, title: str) -> Article | None:
        """根据标题获取文章"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )

        return result.scalars().all()

    async def get_multi_by_author_with_status(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        status: ArticleStatus = ArticleStatus.ALL,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[list[Article], int]:
        """根据状态获取指定作者的文章列表和总数（优化版本）"""

        # 构建基础查询条件
        base_query = select(self.model).where(self.model.author_id == author_id)

        # 根据状态添加过滤条件
        if status == ArticleStatus.DRAFT:
            base_query = base_query.where(self.model.is_published == False)
        elif status == ArticleStatus.PUBLISHED_APPROVED:
            base_query = base_query.where(
                self.model.is_published == True, self.model.is_approved == True
            )
        elif status == ArticleStatus.PUBLISHED_PENDING:
            base_query = base_query.where(
                self.model.is_published == True, self.model.is_approved == False
            )
        elif status == ArticleStatus.PUBLISHED_REJECTED:
            # 假设被拒绝的文章有一个rejected字段，或者通过其他方式标识
            # 这里暂时使用已发布但未通过审核来表示
            base_query = base_query.where(
                self.model.is_published == True, self.model.is_approved == False
            )
        # ArticleStatus.ALL 不需要额外过滤条件

        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 获取文章列表
        articles_query = (
            base_query.options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.updated_at.desc())
        )

        articles_result = await db.execute(articles_query)
        articles = articles_result.scalars().all()

        return articles, total

    async def get_multi_by_author_with_review(
        self,
        db: AsyncSession,
        *,
        author_id: int,
        status: ArticleStatus = ArticleStatus.ALL,
        skip: int = 0,
        limit: int = 100,
    ) -> tuple[list[tuple[Article, Review | None]], int]:
        """根据状态获取指定作者的文章列表和总数，包含审核信息（优化版本）"""

        # 构建基础查询条件
        base_query = (
            select(self.model, Review)
            .outerjoin(
                Review,
                (Review.content_type == ContentType.ARTICLE) & (Review.content_id == self.model.id),
            )
            .where(self.model.author_id == author_id)
        )

        # 根据状态添加过滤条件
        if status == ArticleStatus.DRAFT:
            base_query = base_query.where(self.model.is_published == False)
        elif status == ArticleStatus.PUBLISHED_APPROVED:
            base_query = base_query.where(
                self.model.is_published == True, self.model.is_approved == True
            )
        elif status == ArticleStatus.PUBLISHED_PENDING:
            base_query = base_query.where(
                self.model.is_published == True, self.model.is_approved == False
            )
        elif status == ArticleStatus.PUBLISHED_REJECTED:
            # 通过review表的状态来判断是否被拒绝
            base_query = base_query.where(
                self.model.is_published == True, Review.status == "rejected"
            )
        # ArticleStatus.ALL 不需要额外过滤条件

        # 获取总数（只计算文章数量）
        count_query = (
            select(func.count(self.model.id))
            .outerjoin(
                Review,
                (Review.content_type == ContentType.ARTICLE) & (Review.content_id == self.model.id),
            )
            .where(self.model.author_id == author_id)
        )

        # 应用相同的状态过滤条件到计数查询
        if status == ArticleStatus.DRAFT:
            count_query = count_query.where(self.model.is_published == False)
        elif status == ArticleStatus.PUBLISHED_APPROVED:
            count_query = count_query.where(
                self.model.is_published == True, self.model.is_approved == True
            )
        elif status == ArticleStatus.PUBLISHED_PENDING:
            count_query = count_query.where(
                self.model.is_published == True, self.model.is_approved == False
            )
        elif status == ArticleStatus.PUBLISHED_REJECTED:
            count_query = count_query.where(
                self.model.is_published == True, Review.status == "rejected"
            )

        total_result = await db.execute(count_query)
        total = total_result.scalar()

        # 获取文章列表，包含审核信息
        articles_query = (
            base_query.options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.updated_at.desc())
        )

        articles_result = await db.execute(articles_query)
        articles_with_reviews = articles_result.all()

        return articles_with_reviews, total

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取已发布的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
            )
            .where(self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Article, is_published: bool
    ) -> Article:
        """更新文章发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


async def get_drafts(
    self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
) -> list[Article]:
    """获取指定作者的草稿列表

    Args:
        db: 数据库会话
        author_id: 作者ID
        skip: 跳过的记录数
        limit: 返回的最大记录数

    Returns:
        草稿列表
    """
    result = await db.execute(
        select(self.model)
        .where(self.model.author_id == author_id)
        .where(~self.model.is_published)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def get_pending_review(
    self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
) -> list[Article]:
    """获取指定作者的待审核文章列表

    Args:
        db: 数据库会话
        author_id: 作者ID
        skip: 跳过的记录数
        limit: 返回的最大记录数

    Returns:
        待审核文章列表
    """
    result = await db.execute(
        select(self.model)
        .where(self.model.author_id == author_id)
        .where(self.model.is_published & ~self.model.is_approved)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

    async def get_user_articles_with_permission(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        current_user: User | None = None,
        status: ArticleStatus = ArticleStatus.ALL,
        skip: int = 0,
        limit: int = 100,
        include_review: bool = False,
    ) -> tuple[list[Article] | list[tuple[Article, Review | None]], int]:
        """根据权限获取用户文章列表

        Args:
            db: 数据库会话
            user_id: 目标用户ID
            current_user: 当前用户
            status: 文章状态筛选
            skip: 跳过的记录数
            limit: 返回的最大记录数
            include_review: 是否包含审核信息

        Returns:
            文章列表和总数的元组
        """

        # 权限检查：判断是否为作者本人
        is_author = current_user and current_user.id == user_id

        # 如果不是作者本人，只能访问已发布且已审核通过的文章
        if not is_author:
            status = ArticleStatus.PUBLISHED_APPROVED
            include_review = False  # 其他用户不能看到审核信息

        # 根据是否需要审核信息选择不同的查询方法
        if include_review:
            return await self.get_multi_by_author_with_review(
                db=db, author_id=user_id, status=status, skip=skip, limit=limit
            )
        else:
            return await self.get_multi_by_author_with_status(
                db=db, author_id=user_id, status=status, skip=skip, limit=limit
            )


article = CRUDArticle(Article)
