from fastapi.encoders import jsonable_encoder
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app.crud.base import CRUDBase
from app.models.comment import Comment, CommentType
from app.schemas.comment import CommentCreate, CommentUpdate


class CRUDComment(CRUDBase[Comment, CommentCreate, CommentUpdate]):
    async def create(self, db: AsyncSession, *, obj_in: CommentCreate, author_id: int) -> Comment:
        """创建评论"""
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data, author_id=author_id)
        # 如果是回复评论，reply_to_id 应该存储被回复评论的ID，而不是用户ID
        # 这样前端就能知道具体回复的是哪条评论
        if obj_in.parent_id:
            db_obj.reply_to_id = obj_in.parent_id
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_by_article(
        self, db: AsyncSession, *, article_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取文章的评论，扁平化结构"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.asc())
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 手动分页
        top_level_comments = [c for c in all_comments if not c.parent_id]
        paginated_top_level = top_level_comments[skip : skip + limit]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出相关的回复
        relevant_replies = [c for c in all_comments if c.parent_id in paginated_top_level_ids]

        return paginated_top_level + relevant_replies

    async def get_by_video(
        self, db: AsyncSession, *, video_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取视频的评论，扁平化结构"""
        query = (
            select(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
            .options(joinedload(self.model.author))
            .order_by(self.model.created_at.asc())
        )
        result = await db.execute(query)
        all_comments = result.scalars().all()

        # 手动分页
        top_level_comments = [c for c in all_comments if not c.parent_id]
        paginated_top_level = top_level_comments[skip : skip + limit]

        # 获取这些分页后的顶层评论的ID
        paginated_top_level_ids = {c.id for c in paginated_top_level}

        # 筛选出相关的回复
        relevant_replies = [c for c in all_comments if c.parent_id in paginated_top_level_ids]

        return paginated_top_level + relevant_replies

    async def get_by_user(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Comment]:
        """获取用户的评论列表"""
        query = (
            select(self.model)
            .filter(self.model.author_id == author_id)
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def count_by_article(self, db: AsyncSession, *, article_id: int) -> int:
        """统计文章的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.ARTICLE,
                self.model.article_id == article_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()

    async def count_by_video(self, db: AsyncSession, *, video_id: int) -> int:
        """统计视频的评论数量"""
        query = (
            select(func.count())
            .select_from(self.model)
            .filter(
                self.model.comment_type == CommentType.VIDEO,
                self.model.video_id == video_id,
                self.model.is_visible,
            )
        )
        result = await db.execute(query)
        return result.scalar_one()


comment = CRUDComment(Comment)
