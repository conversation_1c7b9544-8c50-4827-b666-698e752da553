import os

from celery import Celery

from app.core.config import settings

task_modules = []
for filename in os.listdir("app/tasks"):
    if filename.endswith(".py") and not filename.startswith("__"):
        module_name = f"app.tasks.{filename[:-3]}"  # 去掉 .py 后缀
        task_modules.append(module_name)

app = Celery(
    "steam_aggregation",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=task_modules,
)

app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
)

app.autodiscover_tasks()
