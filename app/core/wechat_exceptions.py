from typing import Any

from fastapi import HTTPException


class WeChatException(HTTPException):
    """微信相关异常基类"""

    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str | None = None,
        headers: dict[str, Any] | None = None,
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code


class WeChatAPIException(WeChatException):
    """微信API调用异常"""

    def __init__(self, detail: str, error_code: str | None = None):
        super().__init__(
            status_code=502,
            detail=f"微信API调用失败: {detail}",
            error_code=error_code or "WECHAT_API_ERROR",
        )


class WeChatBindException(WeChatException):
    """微信绑定异常"""

    def __init__(self, detail: str, error_code: str | None = None):
        super().__init__(
            status_code=400, detail=detail, error_code=error_code or "WECHAT_BIND_ERROR"
        )


class WeChatQRCodeException(WeChatException):
    """微信二维码异常"""

    def __init__(self, detail: str, error_code: str | None = None):
        super().__init__(
            status_code=400,
            detail=f"二维码操作失败: {detail}",
            error_code=error_code or "WECHAT_QR_ERROR",
        )


class WeChatAuthException(WeChatException):
    """微信认证异常"""

    def __init__(self, detail: str, error_code: str | None = None):
        super().__init__(
            status_code=401,
            detail=f"微信认证失败: {detail}",
            error_code=error_code or "WECHAT_AUTH_ERROR",
        )


# 常用异常实例
class WeChatErrors:
    """微信错误常量"""

    # 绑定相关错误
    OPENID_ALREADY_BOUND = WeChatBindException("该微信账号已绑定其他用户", "OPENID_ALREADY_BOUND")

    USERNAME_ALREADY_BOUND = WeChatBindException(
        "该用户名已绑定其他微信账号", "USERNAME_ALREADY_BOUND"
    )

    INVALID_VERIFICATION_CODE = WeChatBindException(
        "验证码无效或已过期", "INVALID_VERIFICATION_CODE"
    )

    USER_NOT_FOUND = WeChatBindException("用户不存在", "USER_NOT_FOUND")

    WECHAT_NOT_BOUND = WeChatBindException("该用户未绑定微信账号", "WECHAT_NOT_BOUND")

    # API相关错误
    ACCESS_TOKEN_FAILED = WeChatAPIException("获取访问令牌失败", "ACCESS_TOKEN_FAILED")

    QR_CODE_CREATE_FAILED = WeChatAPIException("创建二维码失败", "QR_CODE_CREATE_FAILED")

    USER_INFO_FAILED = WeChatAPIException("获取用户信息失败", "USER_INFO_FAILED")

    # 二维码相关错误
    QR_CODE_EXPIRED = WeChatQRCodeException("二维码已过期", "QR_CODE_EXPIRED")

    QR_CODE_NOT_FOUND = WeChatQRCodeException("二维码不存在", "QR_CODE_NOT_FOUND")

    # 认证相关错误
    INVALID_OPENID = WeChatAuthException("无效的OpenID", "INVALID_OPENID")

    AUTHENTICATION_REQUIRED = WeChatAuthException("需要微信认证", "AUTHENTICATION_REQUIRED")

    # 频率限制错误
    TOO_MANY_ATTEMPTS = WeChatBindException("操作过于频繁，请稍后再试", "TOO_MANY_ATTEMPTS")
