"""统一权限系统

基于 RBAC (Role-Based Access Control) 模型，支持：
1. 资源级权限控制
2. 所有权检查
3. 动态权限分配
4. 权限继承
5. 条件权限检查
6. 权限缓存
"""

import logging
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any

from fastapi import HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.user import User, UserRole

# 配置日志
logger = logging.getLogger(__name__)


# 权限系统配置
@dataclass
class PermissionConfig:
    """权限系统配置"""

    cache_enabled: bool = True
    cache_ttl: int = 300  # 5分钟
    strict_mode: bool = True
    audit_enabled: bool = True
    max_cache_size: int = 1000


# 全局配置实例
permission_config = PermissionConfig(
    cache_enabled=True,
    cache_ttl=300,  # 5分钟
    strict_mode=False,
    audit_enabled=True,
)


class ResourceType(str, Enum):
    """资源类型枚举"""

    USER = "user"
    ARTICLE = "article"
    VIDEO = "video"
    COMMENT = "comment"
    SYSTEM = "system"
    FOLDER = "folder"
    ROLE = "role"
    PERMISSION = "permission"
    CATEGORY = "category"
    TAG = "tag"


class Action(str, Enum):
    """操作类型枚举"""

    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"  # 管理权限，包含所有操作

    # 特殊操作
    PUBLISH = "publish"
    APPROVE = "approve"
    FOLLOW = "follow"
    UNFOLLOW = "unfollow"  # 取消关注
    LIKE = "like"  # 点赞操作
    COMMENT = "comment"  # 评论操作
    SHARE = "share"  # 分享操作
    DOWNLOAD = "download"  # 下载操作
    UPLOAD = "upload"  # 上传操作


class Scope(str, Enum):
    """权限范围枚举"""

    OWN = "own"  # 仅自己的资源
    ALL = "all"  # 所有资源
    PUBLIC = "public"  # 公开资源
    DEPARTMENT = "department"  # 部门资源
    GROUP = "group"  # 组资源
    APPROVED = "approved"  # 已审核资源


@dataclass
class Permission:
    """权限定义"""

    resource: ResourceType
    action: Action
    scope: Scope = Scope.OWN
    conditions: dict[str, Any] | None = field(default_factory=dict)
    expires_at: datetime | None = None
    description: str | None = None

    def __str__(self) -> str:
        base = f"{self.resource}:{self.action}:{self.scope}"
        if self.conditions:
            conditions_str = ",".join(f"{k}={v}" for k, v in self.conditions.items())
            base += f"?{conditions_str}"
        return base

    def __hash__(self) -> int:
        """使Permission可以作为字典键和集合元素"""
        return hash(
            (
                self.resource,
                self.action,
                self.scope,
                tuple(sorted(self.conditions.items())) if self.conditions else (),
            )
        )

    def __eq__(self, other) -> bool:
        """权限相等性比较"""
        if not isinstance(other, Permission):
            return False
        return (
            self.resource == other.resource
            and self.action == other.action
            and self.scope == other.scope
            and self.conditions == other.conditions
        )

    @classmethod
    def from_string(cls, permission_str: str) -> "Permission":
        """从字符串创建权限对象"""
        # 分离基础权限和条件
        if "?" in permission_str:
            base_part, conditions_part = permission_str.split("?", 1)
            conditions = {}
            for condition in conditions_part.split(","):
                if "=" in condition:
                    key, value = condition.split("=", 1)
                    conditions[key.strip()] = value.strip()
        else:
            base_part = permission_str
            conditions = {}

        parts = base_part.split(":")
        if len(parts) != 3:
            raise ValueError(f"Invalid permission format: {permission_str}")

        return cls(
            resource=ResourceType(parts[0]),
            action=Action(parts[1]),
            scope=Scope(parts[2]),
            conditions=conditions if conditions else None,
        )

    def is_expired(self) -> bool:
        """检查权限是否已过期"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at

    def matches(self, other: "Permission") -> bool:
        """检查权限是否匹配（支持通配符和继承）"""
        # 基础匹配
        if (
            self.resource != other.resource
            or self.action != other.action
            or self.scope != other.scope
        ):
            return False

        # 条件匹配
        if self.conditions and other.conditions:
            for key, value in other.conditions.items():
                if key not in self.conditions or self.conditions[key] != value:
                    return False

        return True


class Role(str, Enum):
    """用户角色枚举"""

    SUPER_ADMIN = "super_admin"  # 超级管理员
    ADMIN = "admin"  # 管理员
    MODERATOR = "moderator"  # 版主
    USER = "user"  # 普通用户
    GUEST = "guest"  # 访客


# 预定义权限常量
# 权限缓存类
class PermissionCache:
    """权限缓存管理"""

    def __init__(self, max_size: int = 1000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl
        self._cache: dict[str, dict[str, Any]] = {}
        self._access_times: dict[str, datetime] = {}

    def _generate_key(self, user_id: int, permission: Permission) -> str:
        """生成缓存键"""
        return f"user:{user_id}:perm:{hash(permission)}"

    def get(self, user_id: int, permission: Permission) -> bool | None:
        """获取缓存的权限检查结果"""
        if not permission_config.cache_enabled:
            return None

        key = self._generate_key(user_id, permission)

        if key not in self._cache:
            return None

        # 检查是否过期
        cache_time = self._access_times.get(key)
        if cache_time and (datetime.utcnow() - cache_time).seconds > self.ttl:
            self._remove(key)
            return None

        # 更新访问时间
        self._access_times[key] = datetime.utcnow()
        return self._cache[key].get("result")

    def set(self, user_id: int, permission: Permission, result: bool) -> None:
        """设置缓存"""
        if not permission_config.cache_enabled:
            return

        key = self._generate_key(user_id, permission)

        # 如果缓存已满，移除最旧的条目
        if len(self._cache) >= self.max_size:
            self._evict_oldest()

        self._cache[key] = {"result": result, "permission": permission, "user_id": user_id}
        self._access_times[key] = datetime.utcnow()

    def _remove(self, key: str) -> None:
        """移除缓存条目"""
        self._cache.pop(key, None)
        self._access_times.pop(key, None)

    def _evict_oldest(self) -> None:
        """移除最旧的缓存条目"""
        if not self._access_times:
            return

        oldest_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove(oldest_key)

    def clear_user_cache(self, user_id: int) -> None:
        """清除特定用户的缓存"""
        keys_to_remove = []
        for key, data in self._cache.items():
            if data.get("user_id") == user_id:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            self._remove(key)

    def clear_all(self) -> None:
        """清除所有缓存"""
        self._cache.clear()
        self._access_times.clear()


# 全局权限缓存实例
permission_cache = PermissionCache(
    max_size=permission_config.max_cache_size, ttl=permission_config.cache_ttl
)


def get_permission_from_string(permission_str: str) -> Permission:
    """从字符串获取权限对象"""
    return Permission.from_string(permission_str)


def clear_user_permissions_cache(user_id: int) -> None:
    """清除指定用户的权限缓存"""
    permission_cache.clear_user_cache(user_id)


def clear_all_permissions_cache() -> None:
    """清除所有权限缓存"""
    permission_cache.clear_all()


def update_permission_config(**kwargs) -> None:
    """更新权限配置"""
    global permission_config
    for key, value in kwargs.items():
        if hasattr(permission_config, key):
            setattr(permission_config, key, value)
        else:
            logger.warning(f"Unknown permission config key: {key}")


class PermissionChecker:
    """统一权限检查器"""

    @staticmethod
    async def get_user_permissions(db: AsyncSession, user: User) -> set[Permission]:
        """从数据库获取用户的所有权限"""
        if not user or not user.role_id:
            return set()

        # 使用 selectinload 预加载角色的权限
        result = await db.execute(
            select(UserRole)
            .options(selectinload(UserRole.permissions))
            .where(UserRole.id == user.role_id)
        )
        role = result.scalar_one_or_none()

        if not role:
            return set()

        # 将数据库中的权限字符串转换为 Permission 对象
        permissions = {get_permission_from_string(p.code) for p in role.permissions}
        return permissions

    @staticmethod
    async def check_permission(
        db: AsyncSession, user: User, permission: Permission, resource: Any = None
    ) -> bool:
        """统一权限检查入口"""
        user_id = 0 if not user else user.id

        # 检查缓存
        cached_result = permission_cache.get(user_id, permission)
        if cached_result is not None:
            return cached_result

        # 执行权限检查
        result = await PermissionChecker._do_check_permission(db, user, permission, resource)

        # 缓存结果
        permission_cache.set(user_id, permission, result)

        # 审计日志
        if permission_config.audit_enabled:
            PermissionChecker._log_permission_check(user, permission, result, resource)

        return result

    @staticmethod
    async def _do_check_permission(
        db: AsyncSession, user: User, permission: Permission, resource: Any = None
    ) -> bool:
        """执行实际的权限检查"""
        # 检查权限是否过期
        if permission.is_expired():
            return False

        # 超级管理员拥有所有权限
        if user and user.is_superuser:
            return True

        # 获取用户权限
        user_permissions = await PermissionChecker.get_user_permissions(db, user)

        # 检查是否有完全匹配的权限
        for user_perm in user_permissions:
            if user_perm.matches(permission):
                return True

        # 检查是否有管理权限（管理权限包含所有操作）
        manage_permission = Permission(
            resource=permission.resource, action=Action.MANAGE, scope=permission.scope
        )
        for user_perm in user_permissions:
            if user_perm.matches(manage_permission):
                return True

        # 检查是否有更高级别的权限
        if permission.scope == Scope.OWN:
            all_permission = Permission(
                resource=permission.resource, action=permission.action, scope=Scope.ALL
            )
            for user_perm in user_permissions:
                if user_perm.matches(all_permission):
                    return True

        # 检查资源所有权
        if resource and user and permission.scope == Scope.OWN:
            resource_owner_id = getattr(resource, "author_id", None) or getattr(
                resource, "user_id", None
            )
            if resource_owner_id == user.id:
                return True

        # 检查公开资源权限
        if permission.scope == Scope.PUBLIC and resource:
            is_public = getattr(resource, "is_published", False) and getattr(
                resource, "is_approved", False
            )
            if is_public and permission.action == Action.READ:
                return True

        return False

    @staticmethod
    async def require_permission(
        db: AsyncSession, user: User, permission: Permission, resource: Any = None
    ) -> None:
        """要求用户拥有指定权限，否则抛出异常"""
        if not await PermissionChecker.check_permission(db, user, permission, resource):
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="需要登录才能执行此操作"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要 {permission} 权限",
                )

    @staticmethod
    async def check_resource_access(
        db: AsyncSession,
        user: User,
        resource_type: ResourceType,
        action: Action,
        resource_owner_id: int | None = None,
        is_public: bool = False,
    ) -> bool:
        """检查用户对特定资源的访问权限"""
        # 构建权限对象
        if is_public and action == Action.READ:
            permission = Permission(resource_type, action, Scope.PUBLIC)
        elif resource_owner_id and user and user.id == resource_owner_id:
            permission = Permission(resource_type, action, Scope.OWN)
        else:
            permission = Permission(resource_type, action, Scope.ALL)

        return await PermissionChecker.check_permission(db, user, permission)

    @staticmethod
    async def require_resource_access(
        db: AsyncSession,
        user: User,
        resource_type: ResourceType,
        action: Action,
        resource_owner_id: int | None = None,
        is_public: bool = False,
    ) -> None:
        """要求用户拥有资源访问权限，否则抛出异常"""
        if not await PermissionChecker.check_resource_access(
            db, user, resource_type, action, resource_owner_id, is_public
        ):
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="需要登录才能访问此资源"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，无法对 {resource_type} 执行 {action} 操作",
                )

    @staticmethod
    def _log_permission_check(
        user, permission: Permission, result: bool, resource: Any = None
    ) -> None:
        """记录权限检查日志"""
        user_info = f"user_id={user.id if user else 'anonymous'}"
        resource_info = f"resource_type={type(resource).__name__}" if resource else "no_resource"
        logger.info(
            f"Permission check: {user_info}, permission={permission}, "
            f"result={result}, {resource_info}"
        )

    @staticmethod
    async def has_permission(db: AsyncSession, user: User, permission: Permission) -> bool:
        """检查用户是否拥有指定权限（兼容性方法）"""
        return await PermissionChecker.check_permission(db, user, permission)
