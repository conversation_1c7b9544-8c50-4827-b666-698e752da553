"""
微信认证模块监控和性能优化配置

提供以下功能：
1. 性能监控指标
2. 缓存优化
3. 限流控制
4. 健康检查
5. 错误统计
"""

import asyncio
import time
from collections import defaultdict, deque
from collections.abc import Callable
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from functools import wraps
from typing import Any

from app.core.config import get_settings
from app.core.logger import get_logger

logger = get_logger(__name__)

settings = get_settings()


class WeChatMetrics:
    """微信认证指标收集器"""

    def __init__(self):
        self.request_count = defaultdict(int)
        self.response_times = defaultdict(list)
        self.error_count = defaultdict(int)
        self.success_count = defaultdict(int)
        self.active_connections = 0
        self.qr_code_scans = 0
        self.bind_attempts = 0
        self.bind_success = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.start_time = datetime.now()

        # 保持最近1小时的数据
        self.time_window = timedelta(hours=1)
        self.cleanup_interval = 300  # 5分钟清理一次
        self.last_cleanup = time.time()

    def record_request(self, endpoint: str, response_time: float, success: bool = True):
        """记录请求指标"""
        current_time = time.time()

        self.request_count[endpoint] += 1
        self.response_times[endpoint].append((current_time, response_time))

        if success:
            self.success_count[endpoint] += 1
        else:
            self.error_count[endpoint] += 1

        # 定期清理旧数据
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_data()
            self.last_cleanup = current_time

    def record_qr_scan(self):
        """记录二维码扫描"""
        self.qr_code_scans += 1

    def record_bind_attempt(self, success: bool = True):
        """记录绑定尝试"""
        self.bind_attempts += 1
        if success:
            self.bind_success += 1

    def record_cache_hit(self, hit: bool = True):
        """记录缓存命中"""
        if hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1

    def _cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = time.time() - self.time_window.total_seconds()

        for endpoint in list(self.response_times.keys()):
            self.response_times[endpoint] = [
                (timestamp, rt)
                for timestamp, rt in self.response_times[endpoint]
                if timestamp > cutoff_time
            ]

    def get_stats(self) -> dict[str, Any]:
        """获取统计信息"""
        uptime = datetime.now() - self.start_time

        # 计算平均响应时间
        avg_response_times = {}
        for endpoint, times in self.response_times.items():
            if times:
                avg_response_times[endpoint] = sum(rt for _, rt in times) / len(times)
            else:
                avg_response_times[endpoint] = 0

        # 计算成功率
        success_rates = {}
        for endpoint in self.request_count:
            total = self.request_count[endpoint]
            success = self.success_count[endpoint]
            success_rates[endpoint] = (success / total * 100) if total > 0 else 0

        # 计算缓存命中率
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = (
            (self.cache_hits / total_cache_requests * 100) if total_cache_requests > 0 else 0
        )

        # 计算绑定成功率
        bind_success_rate = (
            (self.bind_success / self.bind_attempts * 100) if self.bind_attempts > 0 else 0
        )

        return {
            "uptime_seconds": uptime.total_seconds(),
            "uptime_formatted": str(uptime),
            "request_count": dict(self.request_count),
            "error_count": dict(self.error_count),
            "success_count": dict(self.success_count),
            "avg_response_times": avg_response_times,
            "success_rates": success_rates,
            "active_connections": self.active_connections,
            "qr_code_scans": self.qr_code_scans,
            "bind_attempts": self.bind_attempts,
            "bind_success": self.bind_success,
            "bind_success_rate": bind_success_rate,
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "cache_hit_rate": cache_hit_rate,
        }


class RateLimiter:
    """限流器"""

    def __init__(self, max_requests: int = 100, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(deque)

    def is_allowed(self, key: str) -> bool:
        """检查是否允许请求"""
        now = time.time()
        window_start = now - self.time_window

        # 清理过期请求
        while self.requests[key] and self.requests[key][0] < window_start:
            self.requests[key].popleft()

        # 检查是否超过限制
        if len(self.requests[key]) >= self.max_requests:
            return False

        # 记录新请求
        self.requests[key].append(now)
        return True

    def get_remaining(self, key: str) -> int:
        """获取剩余请求次数"""
        now = time.time()
        window_start = now - self.time_window

        # 清理过期请求
        while self.requests[key] and self.requests[key][0] < window_start:
            self.requests[key].popleft()

        return max(0, self.max_requests - len(self.requests[key]))


class WeChatCache:
    """微信认证缓存管理器"""

    def __init__(self):
        self.cache = {}
        self.expire_times = {}
        self.default_ttl = 300  # 5分钟默认过期时间

    def set(self, key: str, value: Any, ttl: int | None = None) -> None:
        """设置缓存"""
        ttl = ttl or self.default_ttl
        expire_time = time.time() + ttl

        self.cache[key] = value
        self.expire_times[key] = expire_time

        # 清理过期缓存
        self._cleanup_expired()

    def get(self, key: str) -> Any | None:
        """获取缓存"""
        if key not in self.cache:
            return None

        # 检查是否过期
        if time.time() > self.expire_times.get(key, 0):
            self.delete(key)
            return None

        return self.cache[key]

    def delete(self, key: str) -> None:
        """删除缓存"""
        self.cache.pop(key, None)
        self.expire_times.pop(key, None)

    def _cleanup_expired(self) -> None:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, expire_time in self.expire_times.items() if current_time > expire_time
        ]

        for key in expired_keys:
            self.delete(key)

    def clear(self) -> None:
        """清空缓存"""
        self.cache.clear()
        self.expire_times.clear()

    def stats(self) -> dict[str, Any]:
        """获取缓存统计"""
        self._cleanup_expired()
        return {
            "total_keys": len(self.cache),
            "memory_usage_estimate": sum(len(str(v)) for v in self.cache.values()),
            "keys": list(self.cache.keys()),
        }


# 全局实例
metrics = WeChatMetrics()
rate_limiter = RateLimiter(
    max_requests=settings.WECHAT_MAX_BIND_ATTEMPTS, time_window=settings.WECHAT_BIND_TIME_WINDOW
)
wechat_cache = WeChatCache()


def monitor_performance(endpoint_name: str):
    """性能监控装饰器"""

    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True

            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Error in {endpoint_name}: {e}")
                raise
            finally:
                response_time = time.time() - start_time
                metrics.record_request(endpoint_name, response_time, success)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = True

            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Error in {endpoint_name}: {e}")
                raise
            finally:
                response_time = time.time() - start_time
                metrics.record_request(endpoint_name, response_time, success)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def rate_limit(key_func: Callable = None, max_requests: int = None):
    """限流装饰器"""

    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 生成限流key
            key = key_func(*args, **kwargs) if key_func else f"{func.__name__}:default"

            # 检查限流
            limiter = RateLimiter(max_requests, 60) if max_requests else rate_limiter
            if not limiter.is_allowed(key):
                from app.core.wechat_exceptions import WeChatErrors

                raise WeChatErrors.RATE_LIMIT_EXCEEDED

            return await func(*args, **kwargs)

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 生成限流key
            key = key_func(*args, **kwargs) if key_func else f"{func.__name__}:default"

            # 检查限流
            limiter = RateLimiter(max_requests, 60) if max_requests else rate_limiter
            if not limiter.is_allowed(key):
                from app.core.wechat_exceptions import WeChatErrors

                raise WeChatErrors.RATE_LIMIT_EXCEEDED

            return func(*args, **kwargs)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


@asynccontextmanager
async def connection_counter():
    """连接计数器上下文管理器"""
    metrics.active_connections += 1
    try:
        yield
    finally:
        metrics.active_connections -= 1


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.checks = {}

    def register_check(self, name: str, check_func: Callable):
        """注册健康检查"""
        self.checks[name] = check_func

    async def run_checks(self) -> dict[str, Any]:
        """运行所有健康检查"""
        results = {}
        overall_healthy = True

        for name, check_func in self.checks.items():
            try:
                if asyncio.iscoroutinefunction(check_func):
                    result = await check_func()
                else:
                    result = check_func()

                results[name] = {
                    "status": "healthy" if result else "unhealthy",
                    "details": result if isinstance(result, dict) else {},
                }

                if not result:
                    overall_healthy = False

            except Exception as e:
                results[name] = {"status": "error", "error": str(e)}
                overall_healthy = False

        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "checks": results,
            "timestamp": datetime.now().isoformat(),
        }


# 全局健康检查器
health_checker = HealthChecker()


# 注册默认健康检查
def check_metrics_health():
    """检查指标收集器健康状态"""
    stats = metrics.get_stats()
    return {
        "uptime": stats["uptime_seconds"],
        "total_requests": sum(stats["request_count"].values()),
        "active_connections": stats["active_connections"],
    }


def check_cache_health():
    """检查缓存健康状态"""
    stats = wechat_cache.stats()
    return {"total_keys": stats["total_keys"], "memory_usage": stats["memory_usage_estimate"]}


# 注册健康检查
health_checker.register_check("metrics", check_metrics_health)
health_checker.register_check("cache", check_cache_health)


def get_monitoring_stats() -> dict[str, Any]:
    """获取完整的监控统计信息"""
    return {
        "metrics": metrics.get_stats(),
        "cache": wechat_cache.stats(),
        "rate_limiter": {"active_keys": len(rate_limiter.requests)},
    }
