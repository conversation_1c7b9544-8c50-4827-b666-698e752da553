import asyncio

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.api.deps import get_db
from app.models.comment import CommentType
from app.schemas.article import ArticleCreate
from app.schemas.comment import CommentCreate
from app.schemas.user import UserCreate


async def seed_comment_data(db: AsyncSession):
    """
    生成评论测试数据
    """
    # 1. 创建测试用户
    user_in = UserCreate(
        username="testuser_comment",
        email="<EMAIL>",
        password="testpassword",
        role_id=1,  # 假设普通用户角色ID为1
    )
    user = await crud.user.get_by_username(db, username=user_in.username)
    if not user:
        user = await crud.user.create(db, obj_in=user_in)

    # 2. 创建测试文章
    article_in = ArticleCreate(
        title="评论测试文章",
        description="这是一篇用于测试评论功能的文章",
        content="文章内容...",
        author_id=user.id,
        is_published=True,
        category_id=1,  # 假设存在 category_id=1 的分类
    )
    article = await crud.article.create(db, obj_in=article_in)

    # 3. 添加评论和回复
    # 一级评论
    comment1_in = CommentCreate(
        content="这是一条一级评论",
        comment_type=CommentType.ARTICLE,
        article_id=article.id,
    )
    comment1 = await crud.comment.create(db, obj_in=comment1_in, author_id=user.id)

    # 回复一级评论
    reply1_to_comment1_in = CommentCreate(
        content="这是对一级评论的回复",
        comment_type=CommentType.ARTICLE,
        article_id=article.id,
        parent_id=comment1.id,
    )
    reply1_to_comment1 = await crud.comment.create(
        db, obj_in=reply1_to_comment1_in, author_id=user.id
    )

    # 回复一级评论的回复
    reply2_to_reply1_in = CommentCreate(
        content="这是对回复的回复",
        comment_type=CommentType.ARTICLE,
        article_id=article.id,
        parent_id=reply1_to_comment1.id,
    )
    await crud.comment.create(db, obj_in=reply2_to_reply1_in, author_id=user.id)

    # 另一条一级评论
    comment2_in = CommentCreate(
        content="这是另一条一级评论",
        comment_type=CommentType.ARTICLE,
        article_id=article.id,
    )
    await crud.comment.create(db, obj_in=comment2_in, author_id=user.id)

    print("评论测试数据已生成")


async def main():
    print("正在生成评论测试数据...")
    db_session_gen = get_db()
    db = await anext(db_session_gen)
    try:
        await seed_comment_data(db)
    finally:
        await anext(db_session_gen, None)
    print("评论测试数据生成完毕。")


if __name__ == "__main__":
    from app.db.session import Base, engine

    async def run_seeding():
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        db_session_gen = get_db()
        db = await anext(db_session_gen)
        try:
            print("正在生成评论测试数据...")
            await seed_comment_data(db)
            print("评论测试数据生成完毕。")
        finally:
            await anext(db_session_gen, None)

    asyncio.run(run_seeding())
