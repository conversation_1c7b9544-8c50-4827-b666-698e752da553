"""Redis连接模块"""

import logging
from typing import Any

import redis.asyncio as redis
from redis.asyncio import Redis
from tenacity import (
    after_log,
    before_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from app.core.config import settings

logger = logging.getLogger(__name__)

# Redis 客户端全局变量
redis_client: Redis = None


@retry(
    stop=stop_after_attempt(3),  # 最多重试3次
    wait=wait_exponential(multiplier=0.5, min=1, max=10),  # 0.5*2^n秒，最少1秒，最多10秒
    retry=retry_if_exception_type(
        (redis.ConnectionError, redis.TimeoutError)
    ),  # 只对连接和超时错误进行重试
    before=before_log(logger, logging.WARNING),
    after=after_log(logger, logging.INFO),
    reraise=True,
)
async def get_redis() -> Redis:
    """获取Redis客户端实例

    使用指数退避算法进行重试:
    - 最多重试3次
    - 等待时间为0.5*2^n秒，最少1秒，最多10秒
    - 只对连接错误和超时错误进行重试
    - 记录重试前的警告日志和重试后的信息日志
    - 重试失败后抛出原始异常

    Returns:
        Redis: Redis客户端实例
    """
    global redis_client
    if redis_client is None:
        try:
            redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
            # 验证连接是否有效
            await redis_client.ping()
            logger.info("Redis连接已建立")
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            redis_client = None  # 重置连接以便重试
            raise
    return redis_client


async def set_key(key: str, value: Any, expire: int = 0) -> bool:
    """设置Redis键值

    Args:
        key: 键名
        value: 值
        expire: 过期时间(秒)，0表示永不过期

    Returns:
        bool: 操作是否成功
    """
    try:
        await redis_client.set(key, value)
        if expire > 0:
            await redis_client.expire(key, expire)
        return True
    except Exception as e:
        logger.error(f"设置Redis键值失败: {e}")
        return False


async def get_key(key: str) -> Any:
    """获取Redis键值

    Args:
        key: 键名

    Returns:
        Any: 键值，如果不存在则返回None
    """
    try:
        return await redis_client.get(key)
    except Exception as e:
        logger.error(f"获取Redis键值失败: {e}")
        return None


async def delete_key(key: str) -> bool:
    """删除Redis键

    Args:
        key: 键名

    Returns:
        bool: 操作是否成功
    """
    try:
        await redis_client.delete(key)
        return True
    except Exception as e:
        logger.error(f"删除Redis键失败: {e}")
        return False
