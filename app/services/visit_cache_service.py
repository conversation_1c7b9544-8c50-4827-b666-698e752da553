"""访问次数缓存服务"""

import json
from typing import Any

from sqlalchemy import select, update

from app import models
from app.db.redis import get_redis
from app.db.session import get_db
from app.services.logger import get_logger

logger = get_logger(__name__)


class VisitCacheService:
    """访问次数缓存服务类"""

    def __init__(self):
        self.redis_client = None
        self.visit_count_prefix = "visit_count"  # 访问次数缓存
        self.hot_visited_prefix = "hot_visited"  # 热门访问内容缓存
        self.cache_expire = 3600 * 24  # 缓存过期时间（秒）- 1天
        self.hot_cache_expire = 1800  # 热点数据缓存过期时间（秒）- 30分钟

    async def _ensure_redis_client(self):
        """确保 Redis 客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    def _get_visit_count_key(self, content_type: str, content_id: int) -> str:
        """获取访问次数缓存键"""
        return f"{self.visit_count_prefix}:{content_type}:{content_id}"

    def _get_hot_visited_key(self, content_type: str) -> str:
        """获取热门访问内容缓存键"""
        return f"{self.hot_visited_prefix}:{content_type}"

    async def get_visit_count(self, content_type: str, content_id: int) -> int | None:
        """获取访问次数（优先从缓存获取，缓存不存在则从数据库获取）"""
        try:
            # 尝试从缓存获取
            await self._ensure_redis_client()
            key = self._get_visit_count_key(content_type, content_id)
            count = await self.redis_client.get(key)

            if count is not None:
                return int(count)

            # 缓存不存在，从数据库获取
            db_count = await self._get_db_visit_count(content_type, content_id)
            if db_count is not None:
                # 更新缓存
                await self.set_visit_count(content_type, content_id, db_count)
            return db_count
        except Exception as e:
            logger.error(f"获取访问次数失败: {e}")
            return None

    async def _get_db_visit_count(self, content_type: str, content_id: int) -> int | None:
        """从数据库获取访问次数"""
        try:
            # 获取数据库会话
            async for db in get_db():
                if content_type == "article":
                    # 获取文章访问次数
                    result = await db.execute(
                        select(models.Article.visit_count).where(models.Article.id == content_id)
                    )
                    count = result.scalar_one_or_none()
                elif content_type == "video":
                    # 获取视频访问次数
                    result = await db.execute(
                        select(models.Video.visit_count).where(models.Video.id == content_id)
                    )
                    count = result.scalar_one_or_none()
                else:
                    count = None
                return count
        except Exception as e:
            logger.error(f"从数据库获取访问次数失败: {e}")
            return None

    async def set_visit_count(self, content_type: str, content_id: int, count: int) -> bool:
        """设置访问次数（同时更新缓存和数据库）"""
        try:
            # 更新缓存
            await self._ensure_redis_client()
            key = self._get_visit_count_key(content_type, content_id)
            await self.redis_client.setex(key, self.cache_expire, count)

            # 更新数据库
            await self._set_db_visit_count(content_type, content_id, count)

            return True
        except Exception as e:
            logger.error(f"设置访问次数缓存失败: {e}")
            return False

    async def _set_db_visit_count(self, content_type: str, content_id: int, count: int) -> bool:
        """设置数据库中的访问次数"""
        try:
            # 获取数据库会话
            async for db in get_db():
                if content_type == "article":
                    # 更新文章访问次数
                    await db.execute(
                        update(models.Article)
                        .where(models.Article.id == content_id)
                        .values(visit_count=count)
                    )
                elif content_type == "video":
                    # 更新视频访问次数
                    await db.execute(
                        update(models.Video)
                        .where(models.Video.id == content_id)
                        .values(visit_count=count)
                    )
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"设置数据库访问次数失败: {e}")
            return False

    async def increment_visit_count(self, content_type: str, content_id: int) -> int | None:
        """增加访问次数（同时更新缓存和数据库）"""
        try:
            # 更新缓存
            await self._ensure_redis_client()
            key = self._get_visit_count_key(content_type, content_id)
            new_count = await self.redis_client.incr(key)
            await self.redis_client.expire(key, self.cache_expire)

            # 更新数据库
            await self._update_db_visit_count(content_type, content_id)

            return new_count
        except Exception as e:
            logger.error(f"增加访问次数失败: {e}")
            return None

    async def _update_db_visit_count(self, content_type: str, content_id: int) -> bool:
        """更新数据库中的访问次数"""
        try:
            # 获取数据库会话
            async for db in get_db():
                if content_type == "article":
                    # 更新文章访问次数
                    await db.execute(
                        update(models.Article)
                        .where(models.Article.id == content_id)
                        .values(visit_count=models.Article.visit_count + 1)
                    )
                elif content_type == "video":
                    # 更新视频访问次数
                    await db.execute(
                        update(models.Video)
                        .where(models.Video.id == content_id)
                        .values(visit_count=models.Video.visit_count + 1)
                    )
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"更新数据库访问次数失败: {e}")
            return False

    async def batch_get_visit_count(
        self, content_items: list[tuple[str, int]]
    ) -> dict[tuple[str, int], int]:
        """批量获取访问次数"""
        result = {}
        try:
            await self._ensure_redis_client()
            # 批量获取访问次数
            pipe = await self.redis_client.pipeline()
            for content_type, content_id in content_items:
                key = self._get_visit_count_key(content_type, content_id)
                pipe.get(key)

            visit_counts = await pipe.execute()

            # 组装结果
            for i, (content_type, content_id) in enumerate(content_items):
                visit_count = visit_counts[i]
                visit_count = int(visit_count) if visit_count is not None else 0
                result[(content_type, content_id)] = visit_count

        except Exception as e:
            logger.error(f"批量获取访问次数失败: {e}")

        return result

    async def update_hot_visited_content(
        self, content_type: str, content_rankings: list[dict[str, Any]]
    ) -> bool:
        """更新热门访问内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_visited_key(content_type)
            data = json.dumps(content_rankings, ensure_ascii=False)
            await self.redis_client.setex(key, self.hot_cache_expire, data)
            return True
        except Exception as e:
            logger.error(f"更新热门访问内容失败: {e}")
            return False

    async def get_hot_visited_content(self, content_type: str) -> list[dict[str, Any]] | None:
        """获取热门访问内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_visited_key(content_type)
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data.decode("utf-8"))
            return None
        except Exception as e:
            logger.error(f"获取热门访问内容失败: {e}")
            return None

    async def clear_content_cache(self, content_type: str, content_id: int) -> bool:
        """清除特定内容的缓存"""
        try:
            await self._ensure_redis_client()
            # 清除访问次数缓存
            visit_count_key = self._get_visit_count_key(content_type, content_id)
            await self.redis_client.delete(visit_count_key)
            return True
        except Exception as e:
            logger.error(f"清除内容缓存失败: {e}")
            return False


# 创建全局实例
visit_cache_service = VisitCacheService()
