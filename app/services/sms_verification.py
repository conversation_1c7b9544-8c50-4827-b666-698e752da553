"""短信验证码服务"""

import random
import string
from datetime import timedelta

from app.api.deps import settings
from app.core.sms_template import SmsTemplate
from app.db.redis import get_redis
from app.schemas.sms import SMSIn
from app.services.logger import get_logger

logger = get_logger(__name__)


class SmsVerification:
    """短信验证码服务"""

    def __init__(self):
        self.redis_client = None
        self.expire_minutes = 5  # 验证码有效期5分钟
        self.max_attempts = 5  # 最大尝试次数

    async def _ensure_redis_client(self) -> None:
        """确保Redis客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    def _generate_code(self, length: int = 6) -> str:
        """生成指定长度的随机验证码"""
        return "".join(random.choices(string.digits, k=length))

    def _get_redis_key(self, phone: str, template_type: SmsTemplate) -> str:
        """生成Redis键名"""
        return f"sms:code:{template_type.name}:{phone}"

    def _get_attempts_key(self, phone: str) -> str:
        """生成验证尝试次数的Redis键名"""
        return f"sms:attempts:{phone}"

    async def send_verification_code(self, phone: str, template_type: SmsTemplate) -> None:
        """发送验证码

        Args:
            phone: 手机号
            template_type: 短信模板类型

        Raises:
            ValueError: 发送过于频繁
        """
        # 检查是否在一分钟内重复发送
        await self._ensure_redis_client()
        redis_key = self._get_redis_key(phone, template_type)
        if await self.redis_client.exists(redis_key):
            ttl = await self.redis_client.ttl(redis_key)
            if ttl > (self.expire_minutes * 60 - 60):  # 如果距离上次发送不足1分钟
                raise ValueError("发送过于频繁，请稍后再试")

        # 生成验证码
        code = self._generate_code()

        # 发送短信
        sms_request = SMSIn(
            phone_numbers=phone,
            sign_name=template_type.sign_name,
            template_code=template_type.template_code,
            template_param='{"code":"' + code + '"}',
        )

        try:
            # 先存储验证码到Redis，设置过期时间
            await self.redis_client.setex(redis_key, timedelta(minutes=self.expire_minutes), code)

            # 发送短信验证码
            if settings.MODE == "dev":
                logger.info(f"开发模式下不发送短信，验证码为：{code}")
            else:
                # 使用Celery任务发送短信
                from app.tasks.auth import send_sms_task

                send_sms_task.apply_async(args=[sms_request.model_dump()])
            logger.info(f"验证码发送任务已创建，手机号：{phone}，模板类型：{template_type.name}")
        except Exception as e:
            # 发送失败时删除Redis中的验证码
            await self.redis_client.delete(redis_key)
            logger.error(f"创建验证码发送任务失败：{str(e)}")
            raise

    async def verify_code(self, phone: str, code: str, template_type: SmsTemplate) -> bool:
        """验证验证码

        Args:
            phone: 手机号
            code: 验证码
            template_type: 短信模板类型

        Returns:
            bool: 验证是否成功

        Raises:
            ValueError: 验证码错误次数过多
        """
        await self._ensure_redis_client()
        # 检查尝试次数
        attempts_key = self._get_attempts_key(phone)
        attempts = await self.redis_client.get(attempts_key)
        if attempts and int(attempts) >= self.max_attempts:
            raise ValueError("验证码错误次数过多，请重新获取验证码")

        # 获取存储的验证码
        redis_key = self._get_redis_key(phone, template_type)
        stored_code = await self.redis_client.get(redis_key)

        if not stored_code:
            return False

        # 开发模式下的万能验证码
        from app.core.config import settings

        if settings.MODE == "dev" and code == "000000":
            # 开发模式万能验证码，删除验证码和尝试次数
            await self.redis_client.delete(redis_key, attempts_key)
            return True

        if code == stored_code:
            # 验证成功，删除验证码和尝试次数
            await self.redis_client.delete(redis_key, attempts_key)
            return True
        else:
            # 验证失败，增加尝试次数
            await self.redis_client.incr(attempts_key)
            await self.redis_client.expire(attempts_key, timedelta(minutes=self.expire_minutes))
            return False
