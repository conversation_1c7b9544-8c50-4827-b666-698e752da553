"""认证服务模块

统一管理用户认证、Token 生成和验证等功能
"""

from datetime import timedelta

from passlib.context import CryptContext
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.services.logger import get_logger
from app.services.token_service import TokenService

logger = get_logger(__name__)

# 密码上下文，用于密码哈希和验证
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class AuthService:
    """认证服务类

    统一处理用户认证、Token 管理等功能
    """

    def __init__(self):
        self.token_service = TokenService()
        self.logger = get_logger(__name__)
        # 使用更兼容的密码上下文配置
        try:
            self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        except Exception:
            # 如果bcrypt有问题，使用备用方案
            self.pwd_context = CryptContext(schemes=["pbkdf2_sha256"], deprecated="auto")

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码

        Args:
            plain_password: 明文密码
            hashed_password: 哈希密码

        Returns:
            bool: 密码是否正确
        """
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """获取密码哈希

        Args:
            password: 明文密码

        Returns:
            str: 哈希后的密码
        """
        return self.pwd_context.hash(password)

    async def authenticate_user(
        self, db: AsyncSession, username: str, password: str
    ) -> User | None:
        """认证用户

        Args:
            db: 数据库会话
            username: 用户名
            password: 密码

        Returns:
            User: 认证成功返回用户对象，失败返回 None
        """
        try:
            result = await db.execute(select(User).where(User.username == username))
            user = result.scalar_one_or_none()

            if not user:
                self.logger.warning(f"用户不存在: {username}")
                return None

            if not self.verify_password(password, user.password):
                self.logger.warning(f"密码错误: {username}")
                return None

            self.logger.info(f"用户认证成功: {username}")
            return user

        except Exception as e:
            self.logger.error(f"用户认证失败: {username}, 错误: {str(e)}")
            return None

    async def create_access_token(
        self, data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
    ) -> str:
        """创建访问令牌

        Args:
            data: Token 数据，必须包含 'sub' 字段（用户名）
            expires_delta: 过期时间增量
            device_id: 设备ID

        Returns:
            str: 生成的访问令牌

        Raises:
            ValueError: 当 data 中缺少 'sub' 字段时
        """
        username = data.get("sub")
        token_type = data.get("type", "access")

        if not username:
            raise ValueError("Token data must contain 'sub' field")

        try:
            token = await self.token_service.create_token(
                username=username,
                device_id=device_id,
                expires_delta=expires_delta,
                token_type=token_type,
            )

            self.logger.info(f"访问令牌创建成功: 用户={username}, 设备={device_id}")
            return token

        except Exception as e:
            self.logger.error(f"创建访问令牌失败: 用户={username}, 错误: {str(e)}")
            raise

    async def refresh_token(self, refresh_token: str) -> str | None:
        """刷新令牌

        Args:
            refresh_token: 刷新令牌

        Returns:
            str: 新的访问令牌，失败返回 None
        """
        try:
            # 验证刷新令牌
            token_info = await self.token_service.verify_token(refresh_token)
            if not token_info or token_info.get("type") != "refresh":
                self.logger.warning("无效的刷新令牌")
                return None

            username = token_info.get("username")
            device_id = token_info.get("device_id")

            # 撤销旧的刷新令牌
            await self.token_service.revoke_token(refresh_token)

            # 创建新的访问令牌
            new_token = await self.create_access_token(data={"sub": username}, device_id=device_id)

            self.logger.info(f"令牌刷新成功: 用户={username}")
            return new_token

        except Exception as e:
            self.logger.error(f"刷新令牌失败: {str(e)}")
            return None

    async def validate_token(self, token: str) -> dict | None:
        """验证令牌

        Args:
            token: 待验证的令牌

        Returns:
            dict: 令牌信息，无效返回 None
        """
        try:
            token_info = await self.token_service.verify_token(token)
            if token_info:
                self.logger.debug(f"令牌验证成功: 用户={token_info.get('username')}")
            return token_info

        except Exception as e:
            self.logger.error(f"令牌验证失败: {str(e)}")
            return None

    async def revoke_user_tokens(self, username: str) -> int:
        """撤销用户的所有令牌

        Args:
            username: 用户名

        Returns:
            int: 撤销的令牌数量
        """
        try:
            count = await self.token_service.revoke_user_tokens(username)
            self.logger.info(f"用户令牌撤销完成: 用户={username}, 数量={count}")
            return count

        except Exception as e:
            self.logger.error(f"撤销用户令牌失败: 用户={username}, 错误: {str(e)}")
            return 0

    async def revoke_device_token(self, username: str, device_id: int) -> bool:
        """撤销设备令牌

        Args:
            username: 用户名
            device_id: 设备ID

        Returns:
            bool: 是否撤销成功
        """
        try:
            success = await self.token_service.revoke_device_token(username, device_id)
            if success:
                self.logger.info(f"设备令牌撤销成功: 用户={username}, 设备={device_id}")
            else:
                self.logger.warning(f"设备令牌撤销失败: 用户={username}, 设备={device_id}")
            return success

        except Exception as e:
            self.logger.error(
                f"撤销设备令牌失败: 用户={username}, 设备={device_id}, 错误: {str(e)}"
            )
            return False


# 全局认证服务实例
auth_service = AuthService()
