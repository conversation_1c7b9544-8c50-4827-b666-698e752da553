"""历史记录缓存服务"""

import time

from app.db.redis import get_redis
from app.services.logger import get_logger

logger = get_logger(__name__)


class HistoryCacheService:
    """历史缓存服务类"""

    def __init__(self):
        self.redis_client = None
        self.cache_expire = 604800  # 缓存过期时间（秒）存储1周
        self.history_prefix = "history"  # 历史记录缓存前缀

    async def _ensure_redis_client(self):
        """确保 Redis 客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    def _get_history_key(self, user_id: int, content_type: str) -> str:
        """获取历史记录缓存键"""
        return f"{self.history_prefix}:{user_id}:{content_type}"

    async def set_history(self, user_id: int, content_type: str, content_id: int) -> bool:
        """设置历史记录缓存"""
        try:
            await self._ensure_redis_client()
            key = self._get_history_key(user_id, content_type)
            # 使用当前时间戳作为score
            timestamp = int(time.time())
            await self.redis_client.zadd(
                key,
                {str(content_id): timestamp},
                nx=False,  # 允许更新已存在的记录
            )
            # 限制历史记录数量(保留最近100条)
            await self.redis_client.zremrangebyrank(key, 0, -101)
            # 设置过期时间
            await self.redis_client.expire(key, self.cache_expire)
            return True
        except Exception as e:
            logger.error(f"设置历史记录缓存失败: {e}")
            return False

    async def get_history(
        self, user_id: int, content_type: str, skip: int = 0, limit: int = 100
    ) -> list[int]:
        """获取历史记录缓存"""
        try:
            await self._ensure_redis_client()
            key = self._get_history_key(user_id, content_type)
            # 获取指定范围的记录ID
            content_ids = await self.redis_client.zrevrange(key, skip, skip + limit - 1)
            return [int(cid) for cid in content_ids]
        except Exception as e:
            logger.error(f"获取历史记录缓存失败: {e}")
            return []

    async def clear_user_content_type_cache(self, user_id: int, content_type: str) -> bool:
        """清除用户特定内容类型的历史记录缓存"""
        try:
            await self._ensure_redis_client()
            key = self._get_history_key(user_id, content_type)
            await self.redis_client.delete(key)
            return True
        except Exception as e:
            logger.error(f"清除用户{content_type}历史记录缓存失败: {e}")
            return False
            
    async def clear_user_cache(self, user_id: int) -> bool:
        """清除用户所有历史记录缓存"""
        try:
            await self._ensure_redis_client()
            # 获取用户所有历史记录的键
            keys = await self.redis_client.keys(f"{self.history_prefix}:{user_id}:*")
            if keys:
                await self.redis_client.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"清除用户所有历史记录缓存失败: {e}")
            return False

    async def set_history_batch(self, user_id: int, histories: list[dict]) -> bool:
        """批量设置历史记录缓存"""
        try:
            await self._ensure_redis_client()
            pipe = self.redis_client.pipeline()
            
            for history in histories:
                key = self._get_history_key(user_id, history["content_type"])
                timestamp = int(time.time())
                pipe.zadd(key, {str(history["content_id"]): timestamp})
                pipe.expire(key, self.cache_expire)
                
            await pipe.execute()
            return True
        except Exception as e:
            logger.error(f"批量设置历史记录缓存失败: {e}")
            return False

    async def get_history_with_score(
        self, user_id: int, content_type: str, skip: int = 0, limit: int = 100
    ) -> list[tuple[int, float]]:
        """获取历史记录及其分数（时间戳）"""
        try:
            await self._ensure_redis_client()
            key = self._get_history_key(user_id, content_type)
            results = await self.redis_client.zrevrange(
                key, skip, skip + limit - 1, withscores=True
            )
            return [(int(cid), score) for cid, score in results]
        except Exception as e:
            logger.error(f"获取历史记录缓存失败: {e}")
            return []


history_service = HistoryCacheService()
