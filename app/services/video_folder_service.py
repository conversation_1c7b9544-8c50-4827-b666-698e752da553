from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.video import Video
from app.models.video_folder import VideoFolder


class VideoFolderService:
    """视频文件夹服务类"""

    @staticmethod
    async def create_default_folder(db: AsyncSession, user_id: int) -> VideoFolder:
        """为用户创建默认文件夹

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            VideoFolder: 创建的默认文件夹
        """
        # 检查用户是否已有默认文件夹
        result = await db.execute(
            select(VideoFolder).where(
                VideoFolder.user_id == user_id,
                VideoFolder.is_default == True,
                VideoFolder.is_deleted == False,
            )
        )
        default_folder = result.scalar_one_or_none()

        if default_folder:
            return default_folder

        # 获取用户信息，用于生成路径
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        if not user:
            raise ValueError(f"用户ID {user_id} 不存在")

        # 创建默认文件夹
        default_folder = VideoFolder(
            name="默认文件夹",
            path=f"/user_{user_id}/default",
            user_id=user_id,
            is_default=True,
            is_public=False,
            access_level=0,  # 私有
            sort_order=0,
        )

        db.add(default_folder)
        await db.commit()
        await db.refresh(default_folder)

        return default_folder

    @staticmethod
    async def ensure_all_users_have_default_folders(db: AsyncSession) -> list[VideoFolder]:
        """确保所有用户都有默认文件夹

        Args:
            db: 数据库会话

        Returns:
            List[VideoFolder]: 创建的默认文件夹列表
        """
        # 获取所有用户ID
        result = await db.execute(select(User.id))
        user_ids = [user_id for (user_id,) in result.all()]

        created_folders = []
        for user_id in user_ids:
            # 检查用户是否已有默认文件夹
            result = await db.execute(
                select(VideoFolder).where(
                    VideoFolder.user_id == user_id,
                    VideoFolder.is_default == True,
                    VideoFolder.is_deleted == False,
                )
            )
            default_folder = result.scalar_one_or_none()

            if not default_folder:
                # 创建默认文件夹
                default_folder = await VideoFolderService.create_default_folder(db, user_id)
                created_folders.append(default_folder)

        return created_folders

    @staticmethod
    async def migrate_videos_to_default_folders(db: AsyncSession) -> int:
        """将无文件夹的视频迁移到默认文件夹

        Args:
            db: 数据库会话

        Returns:
            int: 迁移的视频数量
        """
        # 确保所有用户都有默认文件夹
        await VideoFolderService.ensure_all_users_have_default_folders(db)

        # 查找所有无文件夹的视频
        result = await db.execute(
            select(Video).where(Video.folder_id == None, Video.is_deleted == False)
        )
        videos_without_folder = result.scalars().all()

        migrated_count = 0
        for video in videos_without_folder:
            # 获取用户的默认文件夹
            result = await db.execute(
                select(VideoFolder).where(
                    VideoFolder.user_id == video.author_id,
                    VideoFolder.is_default == True,
                    VideoFolder.is_deleted == False,
                )
            )
            default_folder = result.scalar_one_or_none()

            if default_folder:
                # 将视频移动到默认文件夹
                video.folder_id = default_folder.id
                migrated_count += 1

        await db.commit()
        return migrated_count

    @staticmethod
    async def get_folder_tree(
        db: AsyncSession, user_id: int, include_deleted: bool = False
    ) -> list[dict]:
        """获取用户的文件夹树结构

        Args:
            db: 数据库会话
            user_id: 用户ID
            include_deleted: 是否包含已删除的文件夹

        Returns:
            List[dict]: 文件夹树结构
        """
        # 构建查询条件
        query = select(VideoFolder).where(VideoFolder.user_id == user_id)
        if not include_deleted:
            query = query.where(VideoFolder.is_deleted == False)

        # 获取所有文件夹
        result = await db.execute(query)
        folders = result.scalars().all()

        # 构建文件夹树
        folder_map = {
            folder.id: {
                "id": folder.id,
                "name": folder.name,
                "path": folder.path,
                "is_default": folder.is_default,
                "is_public": folder.is_public,
                "access_level": folder.access_level,
                "cover_url": folder.cover_url,
                "video_count": folder.video_count,
                "created_at": folder.created_at.isoformat() if folder.created_at else None,
                "updated_at": folder.updated_at.isoformat() if folder.updated_at else None,
                "children": [],
            }
            for folder in folders
        }

        # 构建树结构
        root_folders = []
        for folder in folders:
            if folder.parent_id is None:
                root_folders.append(folder_map[folder.id])
            else:
                if folder.parent_id in folder_map:
                    folder_map[folder.parent_id]["children"].append(folder_map[folder.id])

        return root_folders

    @staticmethod
    async def soft_delete_folder(
        db: AsyncSession, folder_id: int, user_id: int | None = None
    ) -> bool:
        """软删除文件夹

        Args:
            db: 数据库会话
            folder_id: 文件夹ID
            user_id: 用户ID，用于权限验证

        Returns:
            bool: 是否删除成功
        """
        # 查询文件夹
        query = select(VideoFolder).where(VideoFolder.id == folder_id)
        if user_id is not None:
            query = query.where(VideoFolder.user_id == user_id)

        result = await db.execute(query)
        folder = result.scalar_one_or_none()
        if not folder:
            return False

        # 不允许删除默认文件夹
        if folder.is_default:
            return False

        # 标记为已删除
        folder.is_deleted = True
        folder.deleted_at = datetime.utcnow()

        # 递归删除子文件夹
        for child in folder.children:
            await VideoFolderService.soft_delete_folder(db, child.id)

        # 将文件夹中的视频移动到默认文件夹
        result = await db.execute(
            select(VideoFolder).where(
                VideoFolder.user_id == folder.user_id,
                VideoFolder.is_default == True,
                VideoFolder.is_deleted == False,
            )
        )
        default_folder = result.scalar_one_or_none()

        if default_folder:
            for video in folder.videos:
                if not video.is_deleted:
                    video.folder_id = default_folder.id

        await db.commit()
        return True

    @staticmethod
    async def get_videos_in_same_folder(
        db: AsyncSession, *, video_id: int, folder_id: int, limit: int = 10
    ) -> list[Video]:
        """获取指定文件夹下的其他视频（排除当前视频）

        Args:
            db: 数据库会话
            video_id: 当前视频ID
            folder_id: 文件夹ID
            limit: 返回的视频数量

        Returns:
            视频列表
        """
        result = await db.execute(
            select(Video)
            .where(
                Video.folder_id == folder_id,
                Video.is_deleted == False,
                Video.is_published == True,
                Video.is_approved == True,
            )
            .limit(limit)
        )
        return result.scalars().all()
