"""阿里云发送短信服务"""

from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_console.client import Client as ConsoleClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential

from app.schemas.sms import SMSIn
from app.services.logger import get_logger

logger = get_logger(__name__)


class SmsSendError(Exception):
    """自定义异常类，用于处理短信发送过程中的错误"""

    def __init__(self, message: str, recommend: str = "", is_business_error: bool = False):
        self.message = message
        self.recommend = recommend
        self.is_business_error = is_business_error  # 标记是否为业务错误（如模板不存在等）
        super().__init__(self.message)


class SmsClient:
    @staticmethod
    def create_client() -> Dysmsapi20170525Client:
        """
        使用凭据初始化账号Client
        @return: Client
        @throws Exception
        """
        # 工程代码建议使用更安全的无AK方式，凭据配置方式请参见：https://help.aliyun.com/document_detail/378659.html。
        credential = CredentialClient()
        config = open_api_models.Config(credential=credential)
        # Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = "dysmsapi.aliyuncs.com"
        return Dysmsapi20170525Client(config)

    @staticmethod
    def _should_retry(exception: Exception) -> bool:
        """判断是否需要重试

        Args:
            exception: 异常对象

        Returns:
            bool: 是否需要重试
        """
        if isinstance(exception, SmsSendError):
            return not exception.is_business_error  # 业务错误不重试
        return True  # 其他错误（如网络错误）需要重试

    @staticmethod
    @retry(
        retry=retry_if_exception_type(),  # 根据异常类型判断是否重试
        stop=stop_after_attempt(3),  # 最多重试3次
        wait=wait_exponential(multiplier=1, min=4, max=10),  # 指数退避：4s, 8s, 10s
        before_sleep=lambda retry_state: logger.warning(
            f"第{retry_state.attempt_number}次重试发送短信，等待{retry_state.next_action.sleep}秒"
        ),
    )
    def send_sms(
        sms_in: SMSIn,
    ) -> dict | None:
        """发送短信

        Args:
            sms_in: 短信发送请求

        Returns:
            Optional[dict]: 发送成功时返回响应数据，失败时抛出异常

        Raises:
            SmsSendError: 短信发送失败
        """
        data = sms_in.model_dump()
        client = SmsClient.create_client()
        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
            phone_numbers=data["phone_numbers"],
            sign_name=data["sign_name"],
            template_code=data["template_code"],
            template_param=data["template_param"],
        )
        runtime = util_models.RuntimeOptions()
        try:
            resp = client.send_sms_with_options(send_sms_request, runtime)
            resp_dict = UtilClient.to_jsonstring(resp)
            logger.info("短信发送成功: %s", resp_dict)
            ConsoleClient.log(resp_dict)
            return resp_dict
        except Exception as error:
            logger.error("短信发送失败: %s", str(error))
            is_business_error = False
            recommend = ""

            if hasattr(error, "data"):
                error_data = error.data
                recommend = error_data.get("Recommend", "")
                # 判断是否为业务错误（如模板不存在、签名不存在等）
                error_code = error_data.get("Code", "")
                is_business_error = error_code in [
                    "isv.SMS_TEMPLATE_ILLEGAL",
                    "isv.SMS_SIGNATURE_ILLEGAL",
                    "isv.MOBILE_NUMBER_ILLEGAL",
                    "isv.BUSINESS_LIMIT_CONTROL",
                ]
                if recommend:
                    logger.error("诊断地址: %s", recommend)

            raise SmsSendError(
                str(error),
                recommend,
                is_business_error,
            ) from error
