"""
文章权限服务类

专门处理文章权限逻辑的服务类，包含各种权限检查方法
"""

from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models
from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    ResourceType,
    Role,
    Scope,
)


class ArticlePermissionService:
    """文章权限服务类"""

    @staticmethod
    async def get_accessible_articles_by_category(
        db: AsyncSession,
        category_id: int,
        current_user: models.User | None = None,
        skip: int = 0,
        limit: int = 100,
        show_draft: bool = False,
        show_pending: bool = False,
    ):
        """
        获取用户可访问的文章列表

        Args:
            db: 数据库会话
            category_id: 分类ID
            current_user: 当前用户（可为None，表示游客）
            skip: 跳过的记录数
            limit: 返回的最大记录数
            show_draft: 是否显示草稿
            show_pending: 是否显示待审核

        Returns:
            文章列表和总数
        """
        user_role = PermissionChecker.get_user_role(current_user)

        # 基础查询
        query = (
            select(models.Article)
            .where(models.Article.category_id == category_id)
            .options(
                selectinload(models.Article.tags),
                selectinload(models.Article.author),
            )
        )

        conditions = []

        if user_role in [Role.SUPER_ADMIN, Role.ADMIN]:
            # 管理员可以看到所有文章
            pass  # 不添加任何条件
        elif user_role == Role.USER:
            # 普通用户的权限逻辑
            # 1. 已发布且已审核的文章（公开文章）
            public_condition = and_(
                models.Article.is_published == True, models.Article.is_approved == True
            )
            conditions.append(public_condition)

            # 2. 自己的文章（包括草稿和待审核）
            if current_user:
                own_articles_condition = models.Article.author_id == current_user.id
                conditions.append(own_articles_condition)

            # 3. 根据参数决定是否包含特定状态的文章
            if show_draft and current_user:
                # 显示自己的草稿
                draft_condition = and_(
                    models.Article.is_published == False,
                    models.Article.author_id == current_user.id,
                )
                conditions.append(draft_condition)

            if show_pending and current_user:
                # 显示自己的待审核文章
                pending_condition = and_(
                    models.Article.is_published == True,
                    models.Article.is_approved == False,
                    models.Article.author_id == current_user.id,
                )
                conditions.append(pending_condition)

        else:  # Role.GUEST
            # 游客只能看到已发布且已审核的文章
            public_condition = and_(
                models.Article.is_published == True, models.Article.is_approved == True
            )
            conditions.append(public_condition)

        # 应用条件
        if conditions:
            if len(conditions) == 1:
                query = query.where(conditions[0])
            else:
                query = query.where(or_(*conditions))

        # 获取总数
        count_query = select(models.Article).where(models.Article.category_id == category_id)
        if conditions:
            if len(conditions) == 1:
                count_query = count_query.where(conditions[0])
            else:
                count_query = count_query.where(or_(*conditions))

        from sqlalchemy import func

        count_query = count_query.with_only_columns(func.count().label("count"))
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        articles = result.scalars().all()

        return articles, total

    @staticmethod
    async def check_article_access(
        db: AsyncSession, content: models.Article, current_user: models.User | None = None
    ) -> bool:
        """
        检查文章访问权限

        Args:
            db: 数据库会话
            content: 文章对象
            current_user: 当前用户

        Returns:
            是否有权访问
        """
        if not content:
            return False

        # 检查对所有文章的读取权限
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.ALL)
        ):
            return True

        # 检查对自己文章的读取权限
        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.OWN)
            ):
                return True

        # 检查对公开文章的读取权限
        if content.is_published and content.is_approved:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.READ, Scope.PUBLIC)
            ):
                return True

        return False

    @staticmethod
    async def check_article_update_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> bool:
        """
        检查文章更新权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def check_article_delete_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> bool:
        """
        检查文章删除权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.DELETE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.DELETE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def can_create_article(db: AsyncSession, current_user: models.User | None) -> bool:
        """
        检查用户是否可以创建文章
        """
        if not current_user:
            return False

        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.CREATE, Scope.OWN)
        )

    @staticmethod
    async def can_publish_article(
        db: AsyncSession, current_user: models.User, article: models.Article
    ) -> bool:
        """
        检查用户是否可以发布文章
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.ALL)
        ):
            return True

        if article.author_id == current_user.id:
            return await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.OWN)
            )

        return False

    @staticmethod
    async def can_approve_article(db: AsyncSession, current_user: models.User) -> bool:
        """
        检查用户是否可以审核文章
        """
        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.ARTICLE, Action.APPROVE, Scope.ALL)
        )


# 创建服务实例
article_permission_service = ArticlePermissionService()
