"""统一文章服务 - 处理包含统计信息和审核信息的文章列表"""

from typing import Any

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.models.review import Review
from app.services.content_stats_service import content_stats_service
from app.services.logger import get_logger

logger = get_logger(__name__)


class UnifiedArticleService:
    """统一文章服务类，用于处理包含统计信息和审核信息的文章列表"""

    async def get_user_articles_unified(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        current_user: models.User | None = None,
        status: schemas.ArticleStatus = schemas.ArticleStatus.ALL,
        skip: int = 0,
        limit: int = 100,
        include_stats: bool = True,
        include_review: bool = False,
    ) -> schemas.ArticleListWithStatsAndReview:
        """获取用户文章列表（统一接口）
        
        Args:
            db: 数据库会话
            user_id: 目标用户ID
            current_user: 当前用户
            status: 文章状态筛选
            skip: 跳过的记录数
            limit: 返回的最大记录数
            include_stats: 是否包含统计信息
            include_review: 是否包含审核信息
            
        Returns:
            包含统计信息和审核信息的文章列表响应
        """
        # 使用CRUD方法获取文章列表（已包含权限检查）
        articles_data, total = await crud.article.get_user_articles_with_permission(
            db=db,
            user_id=user_id,
            current_user=current_user,
            status=status,
            skip=skip,
            limit=limit,
            include_review=include_review,
        )

        # 处理不同的返回类型
        if include_review:
            # 返回的是 (Article, Review | None) 元组列表
            articles = [article for article, review in articles_data]
            reviews = [review for article, review in articles_data]
        else:
            # 返回的是 Article 列表
            articles = articles_data
            reviews = [None] * len(articles)

        # 构建响应数据
        items = []
        
        # 如果需要统计信息，批量获取
        stats_dict = {}
        if include_stats:
            content_items = [("article", article.id) for article in articles]
            stats_dict = await content_stats_service.get_content_stats(
                db=db,
                content_type="article",
                content_items=content_items,
                user_id=current_user.id if current_user else None,
            )

        # 构建每个文章项
        for i, article in enumerate(articles):
            # 基础文章信息
            article_dict = {
                "id": article.id,
                "title": article.title,
                "description": article.description,
                "cover_url": article.cover_url,
                "author_id": article.author_id,
                "is_published": article.is_published,
                "is_approved": article.is_approved,
                "visit_count": article.visit_count,
                "created_at": article.created_at,
                "updated_at": article.updated_at,
                "category_id": article.category_id,
                "tags": [tag.name for tag in article.tags] if article.tags else [],
                "author": article.author,
            }

            # 添加统计信息
            if include_stats:
                item_key = ("article", article.id)
                content_stats = stats_dict.get(item_key, {})
                article_dict.update({
                    "like_count": content_stats.get("like_count", 0),
                    "favorite_count": content_stats.get("favorite_count", 0),
                    "visit_count": content_stats.get("visit_count", article.visit_count or 0),
                    "is_liked_by_user": content_stats.get("is_liked_by_user", False),
                    "is_favorited_by_user": content_stats.get("is_favorited_by_user", False),
                })
            else:
                # 不包含统计信息时设置默认值
                article_dict.update({
                    "like_count": 0,
                    "favorite_count": 0,
                    "is_liked_by_user": False,
                    "is_favorited_by_user": False,
                })

            # 添加审核信息
            article_dict["review"] = reviews[i] if include_review else None

            items.append(article_dict)

        # 创建响应
        response = schemas.ArticleListWithStatsAndReview(
            total=total,
            items=items
        )

        return response


# 创建服务实例
unified_article_service = UnifiedArticleService()
