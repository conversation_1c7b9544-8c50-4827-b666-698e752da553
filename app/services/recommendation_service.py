"""推荐算法服务模块"""

import json
import logging
from datetime import datetime, timedelta

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas

logger = logging.getLogger(__name__)


class RecommendationService:
    """推荐算法服务类"""

    def __init__(self):
        self.cache_expire = 3600  # 缓存过期时间（秒）

        # 交互权重配置
        self.interaction_weights = {
            "view": 1.0,
            "like": 3.0,
            "favorite": 5.0,
            "comment": 4.0,
            "share": 6.0,
            "click": 2.0,
        }

    async def get_recommendations(
        self,
        db: AsyncSession,
        user_id: int,
        algorithm_type: str | None = None,
        limit: int = 10,
        content_type: str | None = None,
        position: str | None = None,
        exclude_seen: bool = True,
    ) -> schemas.RecommendationResponse:
        """获取推荐内容"""

        # 如果未指定算法，使用混合推荐
        if not algorithm_type:
            algorithm_type = "hybrid"

        # 获取推荐结果
        if algorithm_type == "collaborative":
            items = await self._collaborative_filtering(
                db, user_id, limit, content_type, exclude_seen
            )
        elif algorithm_type == "content_based":
            items = await self._content_based_filtering(
                db, user_id, limit, content_type, exclude_seen
            )
        elif algorithm_type == "hot":
            items = await self._hot_content_recommendation(
                db, user_id, limit, content_type, exclude_seen
            )
        elif algorithm_type == "hybrid":
            items = await self._hybrid_recommendation(
                db, user_id, limit, content_type, exclude_seen
            )
        else:
            # 默认使用热门推荐
            items = await self._hot_content_recommendation(
                db, user_id, limit, content_type, exclude_seen
            )

        # 记录推荐日志
        await self._log_recommendation(db, user_id, algorithm_type, items, position)

        return schemas.RecommendationResponse(
            items=items,
            algorithm_type=algorithm_type,
            total_count=len(items),
            generated_at=datetime.utcnow(),
        )

    async def _collaborative_filtering(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """基于用户的协同过滤推荐"""

        # 获取相似用户
        similar_users = await self._find_similar_users(db, user_id, limit=20)

        if not similar_users:
            # 如果没有相似用户，回退到热门推荐
            return await self._hot_content_recommendation(
                db, user_id, limit, content_type, exclude_seen
            )

        # 获取相似用户喜欢的内容
        similar_user_ids = [user[0] for user in similar_users]

        # 构建查询条件
        query_conditions = [
            models.Like.user_id.in_(similar_user_ids),
            models.Like.is_active,
        ]

        if content_type:
            query_conditions.append(models.Like.content_type == content_type)

        # 排除当前用户已经点赞的内容
        if exclude_seen:
            stmt = select(models.Like.content_type, models.Like.content_id).where(
                models.Like.user_id == user_id,
                models.Like.is_active,
            )
            result = await db.execute(stmt)
            user_liked_content = result.all()

            if user_liked_content:
                for content_type_val, content_id in user_liked_content:
                    query_conditions.append(
                        ~and_(
                            models.Like.content_type == content_type_val,
                            models.Like.content_id == content_id,
                        )
                    )

        # 获取推荐内容
        stmt = (
            select(
                models.Like.content_type,
                models.Like.content_id,
                func.count(models.Like.id).label("like_count"),
            )
            .where(and_(*query_conditions))
            .group_by(models.Like.content_type, models.Like.content_id)
            .order_by(desc("like_count"))
            .limit(limit)
        )
        result = await db.execute(stmt)
        recommended_content = result.all()

        # 转换为推荐项
        items = []
        for content_type_val, content_id, like_count in recommended_content:
            # 验证内容是否存在且已发布
            if await self._is_content_available(db, content_type_val, content_id):
                score = min(like_count / 10.0, 1.0)  # 归一化分数
                items.append(
                    schemas.RecommendationItem(
                        content_type=content_type_val,
                        content_id=content_id,
                        score=score,
                        reason="基于相似用户的喜好推荐",
                    )
                )

        return items

    async def _content_based_filtering(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """基于内容的推荐"""

        # 获取用户的兴趣画像
        user_profile = await crud.user_profile.get_by_user_id(db, user_id=user_id)

        if not user_profile:
            # 如果没有用户画像，基于用户历史行为推荐
            return await self._recommend_by_user_history(
                db, user_id, limit, content_type, exclude_seen
            )

        # 解析用户兴趣标签和偏好分类
        interest_tags = json.loads(user_profile.interest_tags or "{}")
        preferred_categories = json.loads(user_profile.preferred_categories or "{}")

        items = []

        # 基于分类推荐
        if preferred_categories:
            category_items = await self._recommend_by_categories(
                db,
                user_id,
                preferred_categories,
                limit // 2,
                content_type,
                exclude_seen,
            )
            items.extend(category_items)

        # 基于标签推荐
        if interest_tags and len(items) < limit:
            tag_items = await self._recommend_by_tags(
                db,
                user_id,
                interest_tags,
                limit - len(items),
                content_type,
                exclude_seen,
            )
            items.extend(tag_items)

        # 如果推荐数量不足，补充热门内容
        if len(items) < limit:
            hot_items = await self._hot_content_recommendation(
                db, user_id, limit - len(items), content_type, exclude_seen
            )
            items.extend(hot_items)

        return items[:limit]

    async def _hot_content_recommendation(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """热门内容推荐"""

        # 计算热度分数的时间窗口（最近7天）
        time_threshold = datetime.utcnow() - timedelta(days=7)

        # 获取文章热度
        articles_hot = []
        if not content_type or content_type == "article":
            articles_hot = await self._get_hot_articles(
                db, user_id, time_threshold, limit, exclude_seen
            )

        # 获取视频热度
        videos_hot = []
        if not content_type or content_type == "video":
            videos_hot = await self._get_hot_videos(
                db, user_id, time_threshold, limit, exclude_seen
            )

        # 合并并排序
        all_items = articles_hot + videos_hot
        all_items.sort(key=lambda x: x.score, reverse=True)

        return all_items[:limit]

    async def _hybrid_recommendation(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """混合推荐算法"""

        # 分配不同算法的权重
        collaborative_limit = max(1, limit // 3)
        content_based_limit = max(1, limit // 3)
        hot_limit = limit - collaborative_limit - content_based_limit

        items = []

        # 协同过滤推荐
        try:
            collaborative_items = await self._collaborative_filtering(
                db, user_id, collaborative_limit, content_type, exclude_seen
            )
            # 调整分数权重
            for item in collaborative_items:
                item.score *= 0.4
                item.reason = f"协同过滤: {item.reason}"
            items.extend(collaborative_items)
        except Exception as e:
            logger.warning(f"协同过滤推荐失败: {e}")

        # 基于内容的推荐
        try:
            content_items = await self._content_based_filtering(
                db, user_id, content_based_limit, content_type, exclude_seen
            )
            # 调整分数权重
            for item in content_items:
                item.score *= 0.3
                item.reason = f"内容推荐: {item.reason}"
            items.extend(content_items)
        except Exception as e:
            logger.warning(f"基于内容的推荐失败: {e}")

        # 热门推荐
        try:
            hot_items = await self._hot_content_recommendation(
                db, user_id, hot_limit, content_type, exclude_seen
            )
            # 调整分数权重
            for item in hot_items:
                item.score *= 0.3
                item.reason = f"热门推荐: {item.reason}"
            items.extend(hot_items)
        except Exception as e:
            logger.warning(f"热门推荐失败: {e}")

        # 去重并排序
        seen_content = set()
        unique_items = []
        for item in items:
            content_key = (item.content_type, item.content_id)
            if content_key not in seen_content:
                seen_content.add(content_key)
                unique_items.append(item)

        # 按分数排序
        unique_items.sort(key=lambda x: x.score, reverse=True)

        return unique_items[:limit]

    async def _find_similar_users(
        self, db: AsyncSession, user_id: int, limit: int = 20
    ) -> list[tuple[int, float]]:
        """查找相似用户"""

        # 获取当前用户的点赞记录
        stmt = select(models.Like).where(
            models.Like.user_id == user_id,
            models.Like.is_active,
        )
        result = await db.execute(stmt)
        user_likes = result.scalars().all()

        if not user_likes:
            return []

        user_content_set = set((like.content_type, like.content_id) for like in user_likes)

        # 获取其他用户的点赞记录
        stmt = select(models.Like.user_id, models.Like.content_type, models.Like.content_id).where(
            models.Like.user_id != user_id,
            models.Like.is_active,
        )
        result = await db.execute(stmt)
        other_users_likes = result.all()

        # 计算用户相似度
        user_similarities = {}
        for other_user_id, content_type, content_id in other_users_likes:
            if other_user_id not in user_similarities:
                user_similarities[other_user_id] = {"common": 0, "total": set()}

            user_similarities[other_user_id]["total"].add((content_type, content_id))

            if (content_type, content_id) in user_content_set:
                user_similarities[other_user_id]["common"] += 1

        # 计算Jaccard相似度
        similar_users = []
        for other_user_id, data in user_similarities.items():
            if data["common"] > 0:
                union_size = len(user_content_set | data["total"])
                jaccard_similarity = data["common"] / union_size
                similar_users.append((other_user_id, jaccard_similarity))

        # 按相似度排序
        similar_users.sort(key=lambda x: x[1], reverse=True)

        return similar_users[:limit]

    async def _recommend_by_user_history(
        self,
        db: AsyncSession,
        user_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """基于用户历史行为推荐"""

        # 获取用户最近的交互记录
        stmt = (
            select(models.UserInteraction)
            .where(
                models.UserInteraction.user_id == user_id,
                models.UserInteraction.created_at >= datetime.utcnow() - timedelta(days=30),
            )
            .order_by(desc(models.UserInteraction.created_at))
            .limit(50)
        )
        result = await db.execute(stmt)
        recent_interactions = result.scalars().all()

        if not recent_interactions:
            return []

        # 分析用户偏好的内容
        content_scores = {}
        for interaction in recent_interactions:
            content_key = (interaction.content_type, interaction.content_id)
            weight = self.interaction_weights.get(interaction.interaction_type, 1.0)

            if content_key not in content_scores:
                content_scores[content_key] = 0
            content_scores[content_key] += weight * interaction.weight

        # 基于用户偏好的内容找相似内容
        items = []
        for (user_content_type, user_content_id), score in sorted(
            content_scores.items(), key=lambda x: x[1], reverse=True
        )[:10]:
            # 获取相似内容
            similar_content = await self._get_similar_content(
                db, user_content_type, user_content_id, limit=5
            )

            for similar_item in similar_content:
                if content_type and similar_item.content_type != content_type:
                    continue

                # 检查是否需要排除已浏览内容
                if exclude_seen and await self._has_user_seen_content(
                    db, user_id, similar_item.content_type, similar_item.content_id
                ):
                    continue

                # 验证内容是否可用
                if await self._is_content_available(
                    db, similar_item.content_type, similar_item.content_id
                ):
                    items.append(
                        schemas.RecommendationItem(
                            content_type=similar_item.content_type,
                            content_id=similar_item.content_id,
                            score=similar_item.score * (score / max(content_scores.values())),
                            reason="基于您对相似内容的兴趣推荐",
                        )
                    )

                if len(items) >= limit:
                    break

            if len(items) >= limit:
                break

        return items[:limit]

    async def _recommend_by_categories(
        self,
        db: AsyncSession,
        user_id: int,
        preferred_categories: dict[str, float],
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """基于分类推荐"""

        items = []

        # 按分类权重排序
        sorted_categories = sorted(preferred_categories.items(), key=lambda x: x[1], reverse=True)

        for category_name, weight in sorted_categories:
            # 获取分类ID
            stmt = select(models.Category).where(models.Category.name == category_name)
            result = await db.execute(stmt)
            category = result.scalar_one_or_none()
            if not category:
                continue

            # 获取该分类下的热门内容
            category_items = await self._get_category_hot_content(
                db,
                user_id,
                category.id,
                limit=max(1, int(limit * weight)),
                content_type=content_type,
                exclude_seen=exclude_seen,
            )

            for item in category_items:
                item.reason = f"基于您对{category_name}分类的兴趣推荐"
                item.score *= weight

            items.extend(category_items)

            if len(items) >= limit:
                break

        return items[:limit]

    async def _recommend_by_tags(
        self,
        db: AsyncSession,
        user_id: int,
        interest_tags: dict[str, float],
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """基于标签推荐"""

        items = []

        # 按标签权重排序
        sorted_tags = sorted(interest_tags.items(), key=lambda x: x[1], reverse=True)

        for tag_name, weight in sorted_tags:
            # 获取标签ID
            stmt = select(models.Tag).where(models.Tag.name == tag_name)
            result = await db.execute(stmt)
            tag = result.scalar_one_or_none()
            if not tag:
                continue

            # 获取该标签下的内容
            tag_items = await self._get_tag_content(
                db,
                user_id,
                tag.id,
                limit=max(1, int(limit * weight)),
                content_type=content_type,
                exclude_seen=exclude_seen,
            )

            for item in tag_items:
                item.reason = f"基于您对{tag_name}标签的兴趣推荐"
                item.score *= weight

            items.extend(tag_items)

            if len(items) >= limit:
                break

        return items[:limit]

    async def _get_hot_articles(
        self,
        db: AsyncSession,
        user_id: int,
        time_threshold: datetime,
        limit: int,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """获取热门文章"""

        # 构建查询：计算文章热度分数
        stmt = (
            select(
                models.Article.id,
                (
                    func.coalesce(func.count(models.Like.id), 0) * 3
                    + func.coalesce(func.count(models.Favorite.id), 0) * 5
                    + func.coalesce(func.count(models.Comment.id), 0) * 4
                ).label("hot_score"),
            )
            .outerjoin(
                models.Like,
                and_(
                    models.Like.content_type == "article",
                    models.Like.content_id == models.Article.id,
                    models.Like.is_active,
                    models.Like.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Favorite,
                and_(
                    models.Favorite.content_type == "article",
                    models.Favorite.content_id == models.Article.id,
                    models.Favorite.is_active,
                    models.Favorite.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Comment,
                and_(
                    models.Comment.comment_type == "article",
                    models.Comment.article_id == models.Article.id,
                    models.Comment.is_visible,
                    models.Comment.created_at >= time_threshold,
                ),
            )
            .where(models.Article.is_published)
            .group_by(models.Article.id)
            .having(func.count() > 0)  # 至少有一个交互
            .order_by(desc("hot_score"))
            .limit(limit * 2)  # 获取更多候选，用于过滤
        )

        # 排除用户已浏览的内容
        if exclude_seen:
            seen_articles = await self._get_user_seen_content(db, user_id, "article")
            if seen_articles:
                stmt = stmt.where(~models.Article.id.in_(seen_articles))

        result = await db.execute(stmt)
        hot_articles = result.all()

        # 转换为推荐项
        items = []
        max_score = max([score for _, score in hot_articles]) if hot_articles else 1

        for article_id, hot_score in hot_articles[:limit]:
            normalized_score = min(hot_score / max_score, 1.0)
            items.append(
                schemas.RecommendationItem(
                    content_type="article",
                    content_id=article_id,
                    score=normalized_score,
                    reason="热门文章推荐",
                )
            )

        return items

    async def _get_hot_videos(
        self,
        db: AsyncSession,
        user_id: int,
        time_threshold: datetime,
        limit: int,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """获取热门视频"""

        # 构建查询：计算视频热度分数
        query = (
            db.query(
                models.Video.id,
                (
                    func.coalesce(func.count(models.Like.id), 0) * 3
                    + func.coalesce(func.count(models.Favorite.id), 0) * 5
                    + func.coalesce(func.count(models.Comment.id), 0) * 4
                ).label("hot_score"),
            )
            .outerjoin(
                models.Like,
                and_(
                    models.Like.content_type == "video",
                    models.Like.content_id == models.Video.id,
                    models.Like.is_active,
                    models.Like.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Favorite,
                and_(
                    models.Favorite.content_type == "video",
                    models.Favorite.content_id == models.Video.id,
                    models.Favorite.is_active,
                    models.Favorite.created_at >= time_threshold,
                ),
            )
            .outerjoin(
                models.Comment,
                and_(
                    models.Comment.comment_type == "video",
                    models.Comment.video_id == models.Video.id,
                    models.Comment.is_visible,
                    models.Comment.created_at >= time_threshold,
                ),
            )
            .filter(models.Video.is_published)
            .group_by(models.Video.id)
            .having(func.count() > 0)  # 至少有一个交互
            .order_by(desc("hot_score"))
            .limit(limit * 2)  # 获取更多候选，用于过滤
        )

        # 排除用户已浏览的内容
        if exclude_seen:
            seen_videos = self._get_user_seen_content(db, user_id, "video")
            if seen_videos:
                query = query.filter(~models.Video.id.in_(seen_videos))

        hot_videos = query.all()

        # 转换为推荐项
        items = []
        max_score = max([score for _, score in hot_videos]) if hot_videos else 1

        for video_id, hot_score in hot_videos[:limit]:
            normalized_score = min(hot_score / max_score, 1.0)
            items.append(
                schemas.RecommendationItem(
                    content_type="video",
                    content_id=video_id,
                    score=normalized_score,
                    reason="热门视频推荐",
                )
            )

        return items

    async def _get_similar_content(
        self, db: AsyncSession, content_type: str, content_id: int, limit: int = 10
    ) -> list[schemas.RecommendationItem]:
        """获取相似内容"""

        # 从相似度表中查询
        stmt = (
            select(models.ContentSimilarity)
            .where(
                models.ContentSimilarity.source_content_type == content_type,
                models.ContentSimilarity.source_content_id == content_id,
            )
            .order_by(desc(models.ContentSimilarity.similarity_score))
            .limit(limit)
        )
        result = await db.execute(stmt)
        similar_items = result.scalars().all()

        items = []
        for similarity in similar_items:
            if await self._is_content_available(
                db, similarity.target_content_type, similarity.target_content_id
            ):
                items.append(
                    schemas.RecommendationItem(
                        content_type=similarity.target_content_type,
                        content_id=similarity.target_content_id,
                        score=similarity.similarity_score,
                        reason="与您浏览的内容相似",
                    )
                )

        return items

    async def _get_category_hot_content(
        self,
        db: AsyncSession,
        user_id: int,
        category_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """获取分类下的热门内容"""

        items = []
        time_threshold = datetime.utcnow() - timedelta(days=7)

        # 获取分类下的文章
        if not content_type or content_type == "article":
            stmt = (
                select(models.Article.id)
                .where(
                    models.Article.category_id == category_id,
                    models.Article.is_published,
                )
                .limit(limit)
            )
            result = await db.execute(stmt)
            articles = result.all()

            for (article_id,) in articles:
                if exclude_seen and await self._has_user_seen_content(
                    db, user_id, "article", article_id
                ):
                    continue

                items.append(
                    schemas.RecommendationItem(
                        content_type="article",
                        content_id=article_id,
                        score=0.8,  # 基础分数
                        reason="分类推荐",
                    )
                )

        # 获取分类下的视频
        if not content_type or content_type == "video":
            videos = (
                db.query(models.Video.id)
                .filter(
                    models.Video.category_id == category_id,
                    models.Video.is_published,
                )
                .limit(limit)
                .all()
            )

            for (video_id,) in videos:
                if exclude_seen and self._has_user_seen_content(db, user_id, "video", video_id):
                    continue

                items.append(
                    schemas.RecommendationItem(
                        content_type="video",
                        content_id=video_id,
                        score=0.8,  # 基础分数
                        reason="分类推荐",
                    )
                )

        return items[:limit]

    async def _get_tag_content(
        self,
        db: AsyncSession,
        user_id: int,
        tag_id: int,
        limit: int,
        content_type: str | None = None,
        exclude_seen: bool = True,
    ) -> list[schemas.RecommendationItem]:
        """获取标签下的内容"""

        items = []

        # 获取标签下的文章
        if not content_type or content_type == "article":
            stmt = (
                select(models.Article.id)
                .join(models.article_tags)
                .where(
                    models.article_tags.c.tag_id == tag_id,
                    models.Article.is_published,
                )
                .limit(limit)
            )
            result = await db.execute(stmt)
            articles = result.all()

            for (article_id,) in articles:
                if exclude_seen and await self._has_user_seen_content(
                    db, user_id, "article", article_id
                ):
                    continue

                items.append(
                    schemas.RecommendationItem(
                        content_type="article",
                        content_id=article_id,
                        score=0.7,  # 基础分数
                        reason="标签推荐",
                    )
                )

        # 获取标签下的视频
        if not content_type or content_type == "video":
            videos = (
                db.query(models.Video.id)
                .join(models.video_tags)
                .filter(
                    models.video_tags.c.tag_id == tag_id,
                    models.Video.is_published,
                )
                .limit(limit)
                .all()
            )

            for (video_id,) in videos:
                if exclude_seen and self._has_user_seen_content(db, user_id, "video", video_id):
                    continue

                items.append(
                    schemas.RecommendationItem(
                        content_type="video",
                        content_id=video_id,
                        score=0.7,  # 基础分数
                        reason="标签推荐",
                    )
                )

        return items[:limit]

    async def _is_content_available(
        self, db: AsyncSession, content_type: str, content_id: int
    ) -> bool:
        """检查内容是否可用（存在且已发布）"""

        if content_type == "article":
            stmt = (
                select(models.Article)
                .where(
                    models.Article.id == content_id,
                    models.Article.is_published,
                )
                .limit(1)
            )
            result = await db.execute(stmt)
            article = result.scalar_one_or_none()
            return article is not None
        elif content_type == "video":
            stmt = (
                select(models.Video)
                .where(
                    models.Video.id == content_id,
                    models.Video.is_published,
                )
                .limit(1)
            )
            result = await db.execute(stmt)
            video = result.scalar_one_or_none()
            return video is not None

        return False

    async def _has_user_seen_content(
        self, db: AsyncSession, user_id: int, content_type: str, content_id: int
    ) -> bool:
        """检查用户是否已浏览过该内容"""

        # 检查浏览历史
        stmt = (
            select(models.UserBrowseHistory)
            .where(
                models.UserBrowseHistory.user_id == user_id,
                models.UserBrowseHistory.content_type == content_type,
                models.UserBrowseHistory.content_id == content_id,
            )
            .limit(1)
        )
        result = await db.execute(stmt)
        browse_history = result.scalar_one_or_none()

        if browse_history:
            return True

        # 检查点赞记录
        stmt = (
            select(models.Like)
            .where(
                models.Like.user_id == user_id,
                models.Like.content_type == content_type,
                models.Like.content_id == content_id,
                models.Like.is_active,
            )
            .limit(1)
        )
        result = await db.execute(stmt)
        like_record = result.scalar_one_or_none()

        if like_record:
            return True

        # 检查收藏记录
        stmt = (
            select(models.Favorite)
            .where(
                models.Favorite.user_id == user_id,
                models.Favorite.content_type == content_type,
                models.Favorite.content_id == content_id,
                models.Favorite.is_active,
            )
            .limit(1)
        )
        result = await db.execute(stmt)
        favorite_record = result.scalar_one_or_none()

        return favorite_record is not None

    async def _get_user_seen_content(
        self, db: AsyncSession, user_id: int, content_type: str
    ) -> list[int]:
        """获取用户已浏览的内容ID列表"""

        seen_content = set()

        # 从浏览历史获取
        stmt = select(models.UserBrowseHistory.content_id).where(
            models.UserBrowseHistory.user_id == user_id,
            models.UserBrowseHistory.content_type == content_type,
        )
        result = await db.execute(stmt)
        browse_history = result.all()
        seen_content.update([content_id for (content_id,) in browse_history])

        # 从点赞记录获取
        stmt = select(models.Like.content_id).where(
            models.Like.user_id == user_id,
            models.Like.content_type == content_type,
            models.Like.is_active,
        )
        result = await db.execute(stmt)
        like_records = result.all()
        seen_content.update([content_id for (content_id,) in like_records])

        # 从收藏记录获取
        stmt = select(models.Favorite.content_id).where(
            models.Favorite.user_id == user_id,
            models.Favorite.content_type == content_type,
            models.Favorite.is_active,
        )
        result = await db.execute(stmt)
        favorite_records = result.all()
        seen_content.update([content_id for (content_id,) in favorite_records])

        return list(seen_content)

    async def _log_recommendation(
        self,
        db: AsyncSession,
        user_id: int,
        algorithm_type: str,
        items: list[schemas.RecommendationItem],
        position: str | None = None,
    ) -> None:
        """记录推荐日志"""

        try:
            recommended_items_json = json.dumps(
                [
                    {
                        "content_type": item.content_type,
                        "content_id": item.content_id,
                        "score": item.score,
                        "reason": item.reason,
                    }
                    for item in items
                ],
                ensure_ascii=False,
            )

            log_data = schemas.RecommendationLogCreate(
                algorithm_type=algorithm_type,
                recommended_items=recommended_items_json,
                position=position,
            )

            crud.recommendation_log.create_with_user(db=db, obj_in=log_data, user_id=user_id)
        except Exception as e:
            logger.error(f"记录推荐日志失败: {e}")

    async def get_similar_videos(
        self, db: AsyncSession, *, video_id: int, limit: int, user_id: int | None
    ) -> list[models.Video]:
        """获取与指定视频相似的视频列表"""
        similar_items = await self._get_similar_content(
            db, content_type="video", content_id=video_id, limit=limit
        )
        if not similar_items:
            return []

        video_ids = [item.content_id for item in similar_items if item.content_type == "video"]
        if not video_ids:
            return []

        return await crud.video.get_multi_by_ids(db, ids=video_ids)

    async def get_recommended_videos(
        self, db: AsyncSession, *, user_id: int | None, limit: int, exclude_video_id: int | None
    ) -> list[models.Video]:
        """获取推荐视频列表"""
        if user_id:
            # 登录用户：混合推荐
            recommendation_response = await self.get_recommendations(
                db,
                user_id=user_id,
                algorithm_type="hybrid",
                limit=limit,
                content_type="video",
                exclude_seen=True,
            )
            items = recommendation_response.items
        else:
            # 未登录用户：热门推荐
            items = await self._hot_content_recommendation(
                db, user_id=0, limit=limit, content_type="video", exclude_seen=False
            )

        if not items:
            return []

        video_ids = [item.content_id for item in items if item.content_type == "video"]
        if exclude_video_id and exclude_video_id in video_ids:
            video_ids.remove(exclude_video_id)

        if not video_ids:
            return []

        return await crud.video.get_multi_by_ids(db, ids=video_ids)


# 创建全局推荐服务实例
recommendation_service = RecommendationService()
