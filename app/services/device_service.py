"""设备管理服务"""

import hashlib
import json

from fastapi import Request
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from user_agents import parse

from app.models.user_device import UserDevice
from app.services.logger import get_logger

logger = get_logger(__name__)


class DeviceFingerprintService:
    """设备指纹生成服务"""

    @staticmethod
    def generate_fingerprint(request: Request, user_id: int) -> str:
        """生成设备指纹

        Args:
            request: FastAPI请求对象
            user_id: 用户ID

        Returns:
            str: 设备指纹字符串
        """
        # 获取用户代理
        user_agent = request.headers.get("user-agent", "")

        # 获取IP地址
        ip_address = DeviceFingerprintService._get_client_ip(request)

        # 解析用户代理
        parsed_ua = parse(user_agent)

        # 构建指纹数据
        fingerprint_data = {
            "browser": f"{parsed_ua.browser.family}_{parsed_ua.browser.version_string}",
            "os": f"{parsed_ua.os.family}_{parsed_ua.os.version_string}",
            "device": parsed_ua.device.family,
            "ip_subnet": DeviceFingerprintService._get_ip_subnet(ip_address),
            "user_id": user_id,
        }

        # 生成哈希
        fingerprint_str = json.dumps(fingerprint_data, sort_keys=True)
        fingerprint_hash = hashlib.sha256(fingerprint_str.encode()).hexdigest()

        return fingerprint_hash[:32]  # 取前32位作为指纹

    @staticmethod
    def _get_client_ip(request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        # 返回直连IP
        return request.client.host if request.client else "unknown"

    @staticmethod
    def _get_ip_subnet(ip_address: str) -> str:
        """获取IP子网（用于指纹生成，提高稳定性）"""
        try:
            parts = ip_address.split(".")
            if len(parts) == 4:
                # IPv4，取前3段
                return ".".join(parts[:3]) + ".0"
            else:
                # IPv6或其他格式，直接返回
                return ip_address
        except:
            return ip_address

    @staticmethod
    def parse_device_info(request: Request) -> dict[str, str]:
        """解析设备信息

        Args:
            request: FastAPI请求对象

        Returns:
            Dict: 设备信息字典
        """
        user_agent = request.headers.get("user-agent", "")
        parsed_ua = parse(user_agent)

        # 确定设备类型
        device_type = "desktop"
        if parsed_ua.is_mobile:
            device_type = "mobile"
        elif parsed_ua.is_tablet:
            device_type = "tablet"

        # 生成设备名称
        device_name = f"{parsed_ua.os.family}"
        if parsed_ua.device.family and parsed_ua.device.family != "Other":
            device_name = f"{parsed_ua.device.family} - {parsed_ua.os.family}"

        return {
            "device_name": device_name,
            "device_type": device_type,
            "browser_name": parsed_ua.browser.family,
            "browser_version": parsed_ua.browser.version_string,
            "os_name": parsed_ua.os.family,
            "os_version": parsed_ua.os.version_string,
            "user_agent": user_agent,
        }


class DeviceTrustService:
    """设备信任管理服务"""

    def __init__(self):
        self.fingerprint_service = DeviceFingerprintService()

    async def get_or_create_device(
        self, db: AsyncSession, user_id: int, request: Request
    ) -> tuple[UserDevice, bool]:
        """获取或创建设备记录

        Args:
            db: 数据库会话
            user_id: 用户ID
            request: 请求对象

        Returns:
            Tuple[UserDevice, bool]: (设备对象, 是否为新设备)
        """
        # 生成设备指纹
        fingerprint = self.fingerprint_service.generate_fingerprint(request, user_id)

        # 查找现有设备
        result = await db.execute(
            select(UserDevice).filter(
                UserDevice.user_id == user_id,
                UserDevice.device_fingerprint == fingerprint
            )
        )
        device = result.scalar_one_or_none()

        if device:
            # 更新现有设备信息
            ip_address = self.fingerprint_service._get_client_ip(request)
            device.update_login_info(ip_address=ip_address)
            device.calculate_trust_score()
            await db.commit()
            return device, False
        else:
            # 创建新设备
            device_info = self.fingerprint_service.parse_device_info(request)
            ip_address = self.fingerprint_service._get_client_ip(request)

            new_device = UserDevice(
                user_id=user_id,
                device_fingerprint=fingerprint,
                device_name=device_info["device_name"],
                device_type=device_info["device_type"],
                ip_address=ip_address,
                user_agent=device_info["user_agent"],
                browser_name=device_info["browser_name"],
                browser_version=device_info["browser_version"],
                os_name=device_info["os_name"],
                os_version=device_info["os_version"],
            )

            new_device.calculate_trust_score()
            db.add(new_device)
            await db.commit()
            await db.refresh(new_device)

            return new_device, True

    def is_device_trusted(self, device: UserDevice) -> bool:
        """判断设备是否可信

        Args:
            device: 设备对象

        Returns:
            bool: 是否可信
        """
        # 手动标记为信任的设备
        if device.is_trusted:
            return True

        # 被阻止的设备不可信
        if device.is_blocked:
            return False

        # 信任分数大于等于60分的设备
        if device.trust_score >= 60:
            return True

        # 常用设备且信任分数大于等于40分
        if device.is_frequent_device and device.trust_score >= 40:
            return True

        return False

    def requires_verification(self, device: UserDevice) -> bool:
        """判断是否需要额外验证

        Args:
            device: 设备对象

        Returns:
            bool: 是否需要验证
        """
        # 已信任的设备不需要验证
        if self.is_device_trusted(device):
            return False

        # 新设备需要验证
        if device.is_new_device:
            return True

        # 信任分数低的设备需要验证
        if device.trust_score < 40:
            return True

        # 长时间未使用的设备需要验证
        if device.last_login_at:
            from datetime import datetime, timedelta

            if datetime.utcnow() - device.last_login_at > timedelta(days=90):
                return True

        return False

    async def trust_device(self, db: AsyncSession, device: UserDevice) -> UserDevice:
        """手动信任设备

        Args:
            db: 数据库会话
            device: 设备对象

        Returns:
            UserDevice: 更新后的设备对象
        """
        device.is_trusted = True
        device.calculate_trust_score()
        await db.commit()
        await db.refresh(device)
        return device

    async def block_device(
        self, db: AsyncSession, device: UserDevice, reason: str = "用户手动阻止"
    ) -> UserDevice:
        """阻止设备

        Args:
            db: 数据库会话
            device: 设备对象
            reason: 阻止原因

        Returns:
            UserDevice: 更新后的设备对象
        """
        device.is_blocked = True
        device.is_trusted = False
        device.blocked_reason = reason
        device.is_active = False
        await db.commit()
        await db.refresh(device)
        return device
