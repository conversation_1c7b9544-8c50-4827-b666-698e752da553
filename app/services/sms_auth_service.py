"""
SMS 认证服务
提供短信验证码认证相关的业务逻辑
"""

from datetime import timedelta

from fastapi import HTTPException, Request
from jose import JWTError, jwt
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.sms_template import SmsTemplate
from app.models.user import User
from app.models.user_device import UserDevice

# UserInfo 在 sms_auth.py 中本地定义，此处不需要导入
from app.services.auth_service import auth_service
from app.services.device_service import DeviceTrustService
from app.services.logger import get_logger
from app.services.sms_verification import SmsVerification
from app.services.user_service import UserService
from app.services.validate_phone_number import validate_phone_number


class AuthenticationResult:
    """认证结果封装类"""

    def __init__(
        self,
        success: bool,
        access_token: str | None = None,
        user: User | None = None,
        requires_device_verification: bool = False,
        verification_token: str | None = None,
        device_info: dict | None = None,
        message: str | None = None,
    ):
        self.success = success
        self.access_token = access_token
        self.user = user
        self.requires_device_verification = requires_device_verification
        self.verification_token = verification_token
        self.device_info = device_info
        self.message = message


class DeviceVerificationContext:
    """设备验证上下文"""

    def __init__(self, user: User, device: UserDevice, verification_token: str):
        self.user = user
        self.device = device
        self.verification_token = verification_token


class SmsAuthService:
    """SMS 认证服务类

    提供短信验证码认证的核心业务逻辑
    """

    def __init__(self):
        self.sms_service = SmsVerification()
        self.device_service = DeviceTrustService()
        self.user_service = UserService()
        self.logger = get_logger(__name__)

    async def validate_phone_and_code(
        self, phone: str, code: str, template: SmsTemplate = SmsTemplate.LOGIN
    ) -> str:
        """验证手机号格式和验证码

        Args:
            phone: 手机号
            code: 验证码
            template: 短信模板类型

        Returns:
            str: 格式化后的手机号

        Raises:
            HTTPException: 验证失败时抛出异常
        """
        # 验证手机号格式
        validated_phone = validate_phone_number(phone)

        # 验证验证码
        if not await self.sms_service.verify_code(validated_phone, code, template):
            raise HTTPException(status_code=400, detail="验证码错误")

        return validated_phone

    async def create_user_session(self, user: User, device: UserDevice, db: AsyncSession) -> str:
        """创建用户会话

        Args:
            user: 用户对象
            device: 设备对象
            db: 数据库会话

        Returns:
            str: 访问令牌
        """
        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await auth_service.create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires,
            device_id=device.id,
        )

        # 更新最后登录时间
        await UserService.update_last_login(db, user)

        return access_token

    async def handle_new_user_registration(
        self, phone: str, request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """处理新用户注册

        Args:
            phone: 手机号
            request: HTTP请求对象
            db: 数据库会话

        Returns:
            AuthenticationResult: 认证结果
        """
        self.logger.info(f"用户不存在，自动创建新用户：{phone}")

        # 创建新用户
        user = await self.user_service.create_user_by_phone(db, phone)

        # 获取或创建设备记录
        device, _ = await self.device_service.get_or_create_device(db, user.id, request)

        # 创建用户会话
        access_token = await self.create_user_session(user, device, db)

        # 首次注册，直接信任设备
        await self.device_service.trust_device(db, device)

        return AuthenticationResult(success=True, access_token=access_token, user=user)

    async def check_device_trust_status(
        self, user: User, request: Request, db: AsyncSession
    ) -> tuple[UserDevice, bool]:
        """检查设备信任状态

        Args:
            user: 用户对象
            request: HTTP请求对象
            db: 数据库会话

        Returns:
            Tuple[UserDevice, bool]: (设备对象, 是否需要验证)
        """
        # 获取或创建设备记录
        device, is_new_device = await self.device_service.get_or_create_device(db, user.id, request)

        # 检查是否需要设备验证
        requires_verification = self.device_service.requires_verification(device)

        return device, requires_verification

    async def generate_device_verification_token(self, user: User, device: UserDevice) -> str:
        """生成设备验证令牌

        Args:
            user: 用户对象
            device: 设备对象

        Returns:
            str: 设备验证令牌
        """
        return await auth_service.create_access_token(
            data={
                "sub": user.username,
                "device_id": device.id,
                "type": "device_verification",
            },
            expires_delta=timedelta(minutes=10),  # 10分钟有效期
            device_id=device.id,
        )

    async def handle_auth_verification(
        self, phone: str, code: str, request: Request, db: AsyncSession
    ) -> AuthenticationResult:
        """处理认证验证（登录/注册统一接口）

        Args:
            phone: 手机号
            code: 验证码
            request: HTTP请求对象
            db: 数据库会话

        Returns:
            AuthenticationResult: 认证结果
        """
        # 验证手机号和验证码
        validated_phone = await self.validate_phone_and_code(phone, code, SmsTemplate.LOGIN)

        # 查找用户
        user = await UserService.get_user_by_username(db, validated_phone)

        # 如果用户不存在，自动创建新用户
        if not user:
            return await self.handle_new_user_registration(validated_phone, request, db)

        # 检查用户是否激活
        if not user.is_active:
            raise HTTPException(status_code=400, detail="用户未激活")

        # 检查设备信任状态
        device, requires_verification = await self.check_device_trust_status(user, request, db)

        # 如果需要设备验证
        if requires_verification:
            verification_token = await self.generate_device_verification_token(user, device)

            return AuthenticationResult(
                success=True,
                requires_device_verification=True,
                verification_token=verification_token,
                device_info={
                    "device_name": device.device_name,
                    "device_type": device.device_type,
                    "location": device.location or "未知位置",
                    "is_new_device": True,  # 需要验证说明是新设备
                },
                message="检测到新设备登录，需要进行设备验证",
            )

        # 设备可信，直接登录
        access_token = await self.create_user_session(user, device, db)

        return AuthenticationResult(success=True, access_token=access_token, user=user)

    def validate_device_verification_token(self, token: str) -> dict:
        """验证设备验证令牌

        Args:
            token: 设备验证令牌

        Returns:
            dict: 令牌载荷

        Raises:
            HTTPException: 令牌无效时抛出异常
        """
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            username = payload.get("sub")
            device_id = payload.get("device_id")
            token_type = payload.get("type")

            if not username or not device_id or token_type != "device_verification":
                raise HTTPException(status_code=400, detail="无效的验证令牌")

            return payload

        except JWTError as e:
            raise HTTPException(status_code=400, detail="验证令牌已过期或无效") from e

    async def handle_device_verification(
        self, verification_token: str, phone: str, code: str, db: AsyncSession
    ) -> AuthenticationResult:
        """处理设备验证

        Args:
            verification_token: 设备验证令牌
            phone: 手机号
            code: 验证码
            db: 数据库会话

        Returns:
            AuthenticationResult: 认证结果
        """
        # 验证手机号和验证码
        validated_phone = await self.validate_phone_and_code(
            phone, code, SmsTemplate.DEVICE_VERIFICATION
        )
        if validated_phone != phone:
            raise HTTPException(status_code=400, detail="手机号与用户名不匹配")

        # 验证设备验证令牌
        payload = self.validate_device_verification_token(verification_token)
        username = payload["sub"]
        device_id = payload["device_id"]

        # 获取用户
        user = await UserService.get_user_by_username(db, username)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 获取设备并标记为信任
        result = await db.execute(select(UserDevice).filter(UserDevice.id == device_id))
        device = result.scalar_one_or_none()

        if not device or device.user_id != user.id:
            raise HTTPException(status_code=400, detail="设备信息不匹配")

        # 标记设备为信任
        await self.device_service.trust_device(db, device)

        # 创建用户会话
        access_token = await self.create_user_session(user, device, db)

        return AuthenticationResult(success=True, access_token=access_token, user=user)

    async def send_auth_code(self, phone: str, db: AsyncSession) -> dict:
        """发送认证码（登录/注册统一接口）

        Args:
            phone: 手机号
            db: 数据库会话

        Returns:
            dict: 发送结果
        """
        # 验证手机号格式
        validated_phone = validate_phone_number(phone)

        # 检查用户是否存在
        user = await UserService.get_user_by_username(db, validated_phone)

        # 发送验证码
        await self.sms_service.send_verification_code(validated_phone, SmsTemplate.LOGIN)

        return {
            "message": "验证码已发送",
            "phone": validated_phone,
            "user_exists": user is not None,
        }

    async def send_register_code(self, phone: str, db: AsyncSession) -> dict:
        """发送注册验证码

        Args:
            phone: 手机号
            db: 数据库会话

        Returns:
            dict: 发送结果

        Raises:
            HTTPException: 手机号已注册时抛出异常
        """
        # from app.core.exceptions import PhoneAlreadyRegisteredError

        # 验证手机号格式
        validated_phone = validate_phone_number(phone)

        # 检查手机号是否已注册
        # if await UserService.is_phone_registered(db, validated_phone):
        #     raise PhoneAlreadyRegisteredError(validated_phone)

        # 发送验证码
        await self.sms_service.send_verification_code(validated_phone, SmsTemplate.REGISTER)

        return {"message": "验证码已发送"}

    async def send_device_verification_code(
        self, verification_token: str, db: AsyncSession
    ) -> dict:
        """发送设备验证码

        Args:
            verification_token: 设备验证令牌
            db: 数据库会话

        Returns:
            dict: 发送结果
        """
        # 验证设备验证令牌
        payload = self.validate_device_verification_token(verification_token)
        username = payload["sub"]

        # 获取用户
        user = await UserService.get_user_by_username(db, username)
        if not user or not user.username:
            raise HTTPException(status_code=404, detail="用户不存在或未绑定用户名")

        # 发送设备验证码
        await self.sms_service.send_verification_code(
            user.username, SmsTemplate.DEVICE_VERIFICATION
        )

        return {"message": "设备验证码已发送"}

    async def verify_device_code(
        self, phone: str, code: str, verification_token: str, db: AsyncSession
    ) -> AuthenticationResult:
        """验证设备验证码

        Args:
            phone: 手机号
            code: 验证码
            verification_token: 设备验证令牌
            db: 数据库会话

        Returns:
            AuthenticationResult: 认证结果
        """
        return await self.handle_device_verification(verification_token, phone, code, db)


# 服务实例
sms_auth_service = SmsAuthService()
