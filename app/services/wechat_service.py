"""微信公众号服务"""

import json
import time
import uuid

import httpx
import redis

from app.core.config import settings
from app.services.logger import get_logger

logger = get_logger(__name__)

# Redis客户端
redis_client = redis.from_url(settings.REDIS_URL)


class WeChatService:
    """微信公众号服务类"""

    def __init__(self):
        self.app_id = settings.WECHAT_APP_ID
        self.app_secret = settings.WECHAT_APP_SECRET
        self.access_token_key = "wechat:access_token"

    async def get_access_token(self) -> str:
        """获取微信公众号access_token"""
        # 先从Redis缓存中获取
        cached_token = redis_client.get(self.access_token_key)
        if cached_token:
            return cached_token.decode("utf-8")

        # 缓存中没有，从微信API获取
        url = "https://api.weixin.qq.com/cgi-bin/token"
        params = {
            "grant_type": "client_credential",
            "appid": self.app_id,
            "secret": self.app_secret,
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            data = response.json()

            if "access_token" in data:
                access_token = data["access_token"]
                expires_in = data.get("expires_in", 7200)

                # 缓存到Redis，提前5分钟过期
                redis_client.setex(
                    self.access_token_key, expires_in - 300, access_token
                )

                logger.info("获取微信access_token成功")
                return access_token
            else:
                logger.error(f"获取微信access_token失败: {data}")
                raise Exception(
                    f"获取微信access_token失败: {data.get('errmsg', '未知错误')}"
                )

    async def create_qr_code(self, scene_str: str, expire_seconds: int = 600) -> dict:
        """
        创建临时二维码

        Args:
            scene_str: 场景字符串，用于标识二维码
            expire_seconds: 过期时间（秒），默认10分钟

        Returns:
            包含二维码信息的字典
        """
        access_token = await self.get_access_token()
        url = f"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={access_token}"

        data = {
            "expire_seconds": expire_seconds,
            "action_name": "QR_STR_SCENE",
            "action_info": {"scene": {"scene_str": scene_str}},
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=data)
            result = response.json()

            if "ticket" in result:
                # 构造二维码URL
                qr_url = f"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={result['ticket']}"

                return {
                    "ticket": result["ticket"],
                    "expire_seconds": result["expire_seconds"],
                    "url": result["url"],
                    "qr_url": qr_url,
                    "scene_str": scene_str,
                }
            else:
                logger.error(f"创建二维码失败: {result}")
                raise Exception(f"创建二维码失败: {result.get('errmsg', '未知错误')}")

    async def get_user_info(self, openid: str) -> dict:
        """
        获取用户基本信息

        Args:
            openid: 用户的openid

        Returns:
            用户信息字典
        """
        access_token = await self.get_access_token()
        url = "https://api.weixin.qq.com/cgi-bin/user/info"
        params = {"access_token": access_token, "openid": openid, "lang": "zh_CN"}

        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            result = response.json()

            if "errcode" not in result:
                return result
            else:
                logger.error(f"获取用户信息失败: {result}")
                raise Exception(f"获取用户信息失败: {result.get('errmsg', '未知错误')}")


class QRCodeLoginService:
    """二维码登录服务"""

    def __init__(self):
        self.wechat_service = WeChatService()

    def generate_login_scene(self) -> str:
        """生成登录场景字符串"""
        return f"login_{uuid.uuid4().hex}_{int(time.time())}"

    async def create_login_qr_code(self) -> dict:
        """
        创建登录二维码

        Returns:
            包含二维码信息和场景ID的字典
        """
        scene_str = self.generate_login_scene()
        qr_info = await self.wechat_service.create_qr_code(
            scene_str, expire_seconds=600
        )

        # 在Redis中存储场景信息
        scene_key = f"qr_login:{scene_str}"
        scene_data = {
            "status": "waiting",  # waiting, scanned, confirmed, expired
            "created_at": int(time.time()),
            "expire_at": int(time.time()) + 600,
        }
        redis_client.setex(scene_key, 600, json.dumps(scene_data))

        return {
            "scene_str": scene_str,
            "qr_url": qr_info["qr_url"],
            "expire_seconds": qr_info["expire_seconds"],
        }

    def get_login_status(self, scene_str: str) -> dict:
        """
        获取登录状态

        Args:
            scene_str: 场景字符串

        Returns:
            登录状态信息
        """
        scene_key = f"qr_login:{scene_str}"
        scene_data = redis_client.get(scene_key)

        if not scene_data:
            return {"status": "expired", "message": "二维码已过期"}

        data = json.loads(scene_data.decode("utf-8"))

        # 检查是否过期
        if int(time.time()) > data["expire_at"]:
            redis_client.delete(scene_key)
            return {"status": "expired", "message": "二维码已过期"}

        return data

    def update_login_status(
        self, scene_str: str, status: str, openid: str = None, user_info: dict = None
    ):
        """
        更新登录状态

        Args:
            scene_str: 场景字符串
            status: 新状态
            openid: 用户openid（可选）
            user_info: 用户信息（可选）
        """
        scene_key = f"qr_login:{scene_str}"
        scene_data = redis_client.get(scene_key)

        if scene_data:
            data = json.loads(scene_data.decode("utf-8"))
            data["status"] = status
            data["updated_at"] = int(time.time())

            if openid:
                data["openid"] = openid
            if user_info:
                data["user_info"] = user_info

            # 更新过期时间
            ttl = redis_client.ttl(scene_key)
            if ttl > 0:
                redis_client.setex(scene_key, ttl, json.dumps(data))


# 创建服务实例
wechat_service = WeChatService()
qr_login_service = QRCodeLoginService()
