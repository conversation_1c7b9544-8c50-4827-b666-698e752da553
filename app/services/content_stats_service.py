"""内容统计信息服务"""

from typing import Any, Literal, TypeVar

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models, schemas
from app.services.favorite_cache_service import favorite_cache_service
from app.services.like_cache_service import like_cache_service
from app.services.logger import get_logger
from app.services.visit_cache_service import visit_cache_service

logger = get_logger(__name__)

# 定义内容类型
ContentType = Literal["article", "video"]

# 定义通用内容模型类型
T = TypeVar("T")


class ContentStatsService:
    """内容统计信息服务类，用于处理文章和视频的统计信息"""

    async def get_content_stats(
        self,
        db: AsyncSession,
        content_type: ContentType,
        content_items: list[tuple[str, int]],
        user_id: int | None = None,
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """获取内容统计信息

        Args:
            db: 数据库会话
            content_type: 内容类型 ("article" 或 "video")
            content_items: 内容项列表，每项为 (content_type, content_id) 元组
            user_id: 用户ID，用于获取用户是否点赞/收藏的信息

        Returns:
            包含统计信息的字典，键为内容项元组，值为统计信息字典
        """
        # 批量获取点赞、收藏和访问次数信息
        visit_info = await visit_cache_service.batch_get_visit_count(content_items)
        like_info = await like_cache_service.batch_get_like_info(content_items, user_id)
        favorite_info = await favorite_cache_service.batch_get_favorite_info(content_items, user_id)

        # 从数据库获取缺失的数据
        missing_visit_items = [item for item in content_items if visit_info.get(item) is None]
        missing_like_items = []
        missing_favorite_items = []

        for item_key in content_items:
            like_data = like_info.get(item_key, {})
            if like_data.get("like_count") is None or like_data.get("is_liked") is None:
                missing_like_items.append(item_key)

            favorite_data = favorite_info.get(item_key, {})
            if (
                favorite_data.get("favorite_count") is None
                or favorite_data.get("is_favorited") is None
            ):
                missing_favorite_items.append(item_key)

        # 从数据库获取缺失的访问数据
        if missing_visit_items:
            await self._fetch_missing_visit_data(db, missing_visit_items, visit_info)

        # 从数据库获取缺失的点赞和收藏数据
        if missing_like_items and user_id:
            db_like_info = await crud.like.get_content_likes_batch(
                db, content_items=missing_like_items, user_id=user_id
            )
            like_info.update(db_like_info)

        if missing_favorite_items and user_id:
            db_favorite_info = await crud.favorite.get_content_favorites_batch(
                db, content_items=missing_favorite_items, user_id=user_id
            )
            favorite_info.update(db_favorite_info)

        # 组合所有统计信息
        stats = {}
        for item_key in content_items:
            like_data = like_info.get(item_key, {})
            favorite_data = favorite_info.get(item_key, {})

            stats[item_key] = {
                "like_count": like_data.get("like_count") or 0,
                "favorite_count": favorite_data.get("favorite_count") or 0,
                "visit_count": visit_info.get(item_key) or 0,
                "is_liked_by_user": like_data.get("is_liked", False),
                "is_favorited_by_user": favorite_data.get("is_favorited", False),
            }

        return stats

    async def _fetch_missing_visit_data(
        self,
        db: AsyncSession,
        missing_visit_items: list[tuple[str, int]],
        visit_info: dict[tuple[str, int], int],
    ) -> None:
        """从数据库获取缺失的访问数据

        Args:
            db: 数据库会话
            missing_visit_items: 缺失访问数据的内容项列表
            visit_info: 访问信息字典，将被更新
        """
        for content_type, content_id in missing_visit_items:
            history_records = await db.execute(
                select(models.History).where(
                    models.History.content_type == content_type,
                    models.History.content_id == content_id,
                )
            )
            histories = history_records.scalars().all()
            visit_count = sum(history.visit_count for history in histories)
            visit_info[(content_type, content_id)] = visit_count
            # 更新缓存
            await visit_cache_service.set_visit_count(content_type, content_id, visit_count)

    async def enrich_content_with_stats(
        self,
        db: AsyncSession,
        content_type: ContentType,
        contents: list[T],
        user_id: int | None = None,
    ) -> list[schemas.ArticleWithStats | schemas.VideoWithStats]:
        """为内容列表添加统计信息

        Args:
            db: 数据库会话
            content_type: 内容类型 ("article" 或 "video")
            contents: 内容对象列表
            user_id: 用户ID，用于获取用户是否点赞/收藏的信息

        Returns:
            包含统计信息的内容对象列表
        """
        # 构建内容项列表
        content_items = [(content_type, content.id) for content in contents]

        # 获取统计信息
        stats = await self.get_content_stats(db, content_type, content_items, user_id)

        # 为内容添加统计信息
        contents_with_stats = []
        for content in contents:
            item_key = (content_type, content.id)
            content_stats = stats.get(item_key, {})

            # 创建字典并排除特定字段
            if content_type == "article":
                content_dict = {k: v for k, v in content.__dict__.items() if k != "content"}
                content_dict.update(content_stats)
                contents_with_stats.append(schemas.ArticleWithStats(**content_dict))
            else:  # video
                content_dict = content.__dict__.copy()
                content_dict.update(content_stats)
                contents_with_stats.append(schemas.VideoWithStats(**content_dict))

        return contents_with_stats

    async def create_content_list_response(
        self,
        db: AsyncSession,
        content_type: ContentType,
        contents: list[T],
        total: int,
        user_id: int | None = None,
    ) -> schemas.ArticleListWithStats | schemas.VideoListWithStats:
        """创建包含统计信息的内容列表响应

        Args:
            db: 数据库会话
            content_type: 内容类型 ("article" 或 "video")
            contents: 内容对象列表
            total: 内容总数
            user_id: 用户ID，用于获取用户是否点赞/收藏的信息

        Returns:
            包含统计信息的内容列表响应
        """
        # 为内容添加统计信息
        contents_with_stats = await self.enrich_content_with_stats(
            db, content_type, contents, user_id
        )

        # 创建响应
        response = {"total": total, "items": contents_with_stats}

        return response


# 创建服务实例
content_stats_service = ContentStatsService()
