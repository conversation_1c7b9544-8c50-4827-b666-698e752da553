import logging
from typing import Any

from sqlalchemy.orm import Session

from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    Permissions,
    ResourceType,
    Role,
    Scope,
    clear_user_permissions_cache,
    get_permission_from_string,
)
from app.models.article import Article
from app.models.user import User
from app.models.video import Video

logger = logging.getLogger(__name__)


class UnifiedPermissionService:
    """统一权限服务 - 整合所有权限检查逻辑"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def check_permission(self, user: User | None, permission: Permission, 
                        resource: Any = None) -> bool:
        """统一权限检查入口"""
        return PermissionChecker.check_permission(user, permission, resource)
    
    def require_permission(self, user: User | None, permission: Permission, 
                          resource: Any = None) -> None:
        """要求用户拥有指定权限"""
        PermissionChecker.require_permission(user, permission, resource)
    
    def check_legacy_permission(self, user: User | None, permission_str: str) -> bool:
        """检查旧格式权限（兼容性方法）"""
        permission = get_permission_from_string(permission_str)
        return self.check_permission(user, permission)
    
    def require_legacy_permission(self, user: User | None, permission_str: str) -> None:
        """要求用户拥有旧格式权限（兼容性方法）"""
        permission = get_permission_from_string(permission_str)
        self.require_permission(user, permission)
    
    # 文章权限检查
    def check_article_access(self, user: User | None, article: Article, 
                           action: Action = Action.READ) -> bool:
        """检查文章访问权限"""
        # 构建权限对象
        if action == Action.READ:
            # 读取权限：检查是否为公开文章
            if article.is_published and article.is_approved:
                permission = Permission(ResourceType.ARTICLE, Action.READ, Scope.PUBLIC)
                if self.check_permission(user, permission, article):
                    return True
            
            # 检查是否为作者
            if user and article.author_id == user.id:
                permission = Permission(ResourceType.ARTICLE, Action.READ, Scope.OWN)
                return self.check_permission(user, permission, article)
            
            # 检查管理员权限
            permission = Permission(ResourceType.ARTICLE, Action.READ, Scope.ALL)
            return self.check_permission(user, permission, article)
        
        else:
            # 其他操作：检查所有权或管理权限
            if user and article.author_id == user.id:
                permission = Permission(ResourceType.ARTICLE, action, Scope.OWN)
                if self.check_permission(user, permission, article):
                    return True
            
            permission = Permission(ResourceType.ARTICLE, action, Scope.ALL)
            return self.check_permission(user, permission, article)
        
        return False
    
    def require_article_access(self, user: User | None, article: Article, 
                             action: Action = Action.READ) -> None:
        """要求用户拥有文章访问权限"""
        if not self.check_article_access(user, article, action):
            if not user:
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录才能访问此文章"
                )
            else:
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，无法对文章执行 {action} 操作"
                )
    
    def check_article_update_permission(self, user: User | None, article: Article) -> bool:
        """检查文章更新权限（兼容性方法）"""
        return self.check_article_access(user, article, Action.UPDATE)
    
    def check_article_delete_permission(self, user: User | None, article: Article) -> bool:
        """检查文章删除权限（兼容性方法）"""
        return self.check_article_access(user, article, Action.DELETE)
    
    # 视频权限检查
    def check_video_access(self, user: User | None, video: Video, 
                          action: Action = Action.READ) -> bool:
        """检查视频访问权限"""
        # 构建权限对象
        if action == Action.READ:
            # 读取权限：检查是否为公开视频
            if video.is_published and video.is_approved:
                permission = Permission(ResourceType.VIDEO, Action.READ, Scope.PUBLIC)
                if self.check_permission(user, permission, video):
                    return True
            
            # 检查是否为作者
            if user and video.author_id == user.id:
                permission = Permission(ResourceType.VIDEO, Action.READ, Scope.OWN)
                return self.check_permission(user, permission, video)
            
            # 检查管理员权限
            permission = Permission(ResourceType.VIDEO, Action.READ, Scope.ALL)
            return self.check_permission(user, permission, video)
        
        else:
            # 其他操作：检查所有权或管理权限
            if user and video.author_id == user.id:
                permission = Permission(ResourceType.VIDEO, action, Scope.OWN)
                if self.check_permission(user, permission, video):
                    return True
            
            permission = Permission(ResourceType.VIDEO, action, Scope.ALL)
            return self.check_permission(user, permission, video)
        
        return False
    
    def require_video_access(self, user: User | None, video: Video, 
                           action: Action = Action.READ) -> None:
        """要求用户拥有视频访问权限"""
        if not self.check_video_access(user, video, action):
            if not user:
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录才能访问此视频"
                )
            else:
                from fastapi import HTTPException, status
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，无法对视频执行 {action} 操作"
                )
    
    def check_video_update_permission(self, user: User | None, video: Video) -> bool:
        """检查视频更新权限（兼容性方法）"""
        return self.check_video_access(user, video, Action.UPDATE)
    
    def check_video_delete_permission(self, user: User | None, video: Video) -> bool:
        """检查视频删除权限（兼容性方法）"""
        return self.check_video_access(user, video, Action.DELETE)
    
    def check_move_permission(self, user: User | None, resource: Any) -> bool:
        """检查移动权限（兼容性方法）"""
        if isinstance(resource, Article):
            return self.check_article_access(user, resource, Action.UPDATE)
        elif isinstance(resource, Video):
            return self.check_video_access(user, resource, Action.UPDATE)
        return False
    
    # 批量权限检查
    def get_accessible_articles_by_category(self, user: User | None, 
                                          category_id: int | None = None,
                                          include_unpublished: bool = False) -> list[int]:
        """获取用户可访问的文章ID列表"""
        from sqlalchemy import and_, or_
        
        query = self.db.query(Article.id)
        
        if category_id:
            query = query.filter(Article.category_id == category_id)
        
        if not user:
            # 游客只能看到已发布且已审核的文章
            query = query.filter(
                and_(Article.is_published == True, Article.is_approved == True)
            )
        else:
            user_role = PermissionChecker.get_user_role(user)
            
            if user_role in [Role.SUPER_ADMIN, Role.ADMIN]:
                # 管理员可以看到所有文章
                pass
            elif user_role == Role.MODERATOR:
                # 版主可以看到所有已发布的文章
                if not include_unpublished:
                    query = query.filter(Article.is_published == True)
            else:
                # 普通用户只能看到已发布且已审核的文章，或者自己的文章
                conditions = []
                
                # 公开文章
                conditions.append(
                    and_(Article.is_published == True, Article.is_approved == True)
                )
                
                # 自己的文章
                conditions.append(Article.author_id == user.id)
                
                query = query.filter(or_(*conditions))
        
        return [row[0] for row in query.all()]
    
    def get_accessible_videos_by_category(self, user: User | None, 
                                        category_id: int | None = None,
                                        include_unpublished: bool = False) -> list[int]:
        """获取用户可访问的视频ID列表"""
        from sqlalchemy import and_, or_
        
        query = self.db.query(Video.id)
        
        if category_id:
            query = query.filter(Video.category_id == category_id)
        
        if not user:
            # 游客只能看到已发布且已审核的视频
            query = query.filter(
                and_(Video.is_published == True, Video.is_approved == True)
            )
        else:
            user_role = PermissionChecker.get_user_role(user)
            
            if user_role in [Role.SUPER_ADMIN, Role.ADMIN]:
                # 管理员可以看到所有视频
                pass
            elif user_role == Role.MODERATOR:
                # 版主可以看到所有已发布的视频
                if not include_unpublished:
                    query = query.filter(Video.is_published == True)
            else:
                # 普通用户只能看到已发布且已审核的视频，或者自己的视频
                conditions = []
                
                # 公开视频
                conditions.append(
                    and_(Video.is_published == True, Video.is_approved == True)
                )
                
                # 自己的视频
                conditions.append(Video.author_id == user.id)
                
                query = query.filter(or_(*conditions))
        
        return [row[0] for row in query.all()]
    
    # 缓存管理
    def clear_user_cache(self, user_id: int) -> None:
        """清除用户权限缓存"""
        clear_user_permissions_cache(user_id)
        logger.info(f"Cleared permission cache for user {user_id}")
    
    def get_user_permissions_summary(self, user: User | None) -> dict[str, Any]:
        """获取用户权限摘要"""
        if not user:
            return {
                "role": "guest",
                "permissions": [],
                "can_create_article": False,
                "can_create_video": False,
                "can_manage_users": False,
                "can_manage_system": False
            }
        
        role = PermissionChecker.get_user_role(user)
        permissions = PermissionChecker.get_user_permissions(user)
        
        return {
            "role": role.value,
            "permissions": [str(p) for p in permissions],
            "can_create_article": self.check_permission(user, Permissions.ARTICLE_CREATE),
            "can_create_video": self.check_permission(user, Permissions.VIDEO_CREATE),
            "can_manage_users": self.check_permission(user, Permissions.USER_MANAGE),
            "can_manage_system": self.check_permission(user, Permissions.SYSTEM_MANAGE)
        }