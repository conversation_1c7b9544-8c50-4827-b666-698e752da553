"""点赞缓存服务"""

import json
from typing import Any

from app.db.redis import get_redis
from app.services.logger import get_logger

logger = get_logger(__name__)


class LikeCacheService:
    """点赞缓存服务类"""

    def __init__(self):
        self.redis_client = None
        self.like_count_prefix = "like_count"  # 点赞数量缓存
        self.user_like_prefix = "user_like"  # 用户点赞状态缓存
        self.hot_content_prefix = "hot_content"  # 热点内容缓存
        self.cache_expire = 3600  # 缓存过期时间（秒）
        self.hot_cache_expire = 1800  # 热点数据缓存过期时间（秒）

    async def _ensure_redis_client(self):
        """确保 Redis 客户端已初始化"""
        if self.redis_client is None:
            self.redis_client = await get_redis()

    def _get_like_count_key(self, content_type: str, content_id: int) -> str:
        """获取点赞数量缓存键"""
        return f"{self.like_count_prefix}:{content_type}:{content_id}"

    def _get_user_like_key(self, user_id: int, content_type: str, content_id: int) -> str:
        """获取用户点赞状态缓存键"""
        return f"{self.user_like_prefix}:{user_id}:{content_type}:{content_id}"

    def _get_user_likes_set_key(self, user_id: int, content_type: str) -> str:
        """获取用户点赞集合缓存键"""
        return f"{self.user_like_prefix}_set:{user_id}:{content_type}"

    def _get_hot_content_key(self, content_type: str) -> str:
        """获取热点内容缓存键"""
        return f"{self.hot_content_prefix}:{content_type}"

    async def get_like_count(self, content_type: str, content_id: int) -> int | None:
        """从缓存获取点赞数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_like_count_key(content_type, content_id)
            count = await self.redis_client.get(key)
            return int(count) if count is not None else None
        except Exception as e:
            logger.error(f"获取点赞数量缓存失败: {e}")
            return None

    async def set_like_count(self, content_type: str, content_id: int, count: int) -> bool:
        """设置点赞数量缓存"""
        try:
            await self._ensure_redis_client()
            key = self._get_like_count_key(content_type, content_id)
            await self.redis_client.setex(key, self.cache_expire, count)
            return True
        except Exception as e:
            logger.error(f"设置点赞数量缓存失败: {e}")
            return False

    async def increment_like_count(self, content_type: str, content_id: int) -> int | None:
        """增加点赞数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_like_count_key(content_type, content_id)
            new_count = await self.redis_client.incr(key)
            await self.redis_client.expire(key, self.cache_expire)
            return new_count
        except Exception as e:
            logger.error(f"增加点赞数量失败: {e}")
            return None

    async def decrement_like_count(self, content_type: str, content_id: int) -> int | None:
        """减少点赞数量"""
        try:
            await self._ensure_redis_client()
            key = self._get_like_count_key(content_type, content_id)
            new_count = await self.redis_client.decr(key)
            # 确保不会小于0
            if new_count < 0:
                await self.redis_client.set(key, 0)
                new_count = 0
            await self.redis_client.expire(key, self.cache_expire)
            return new_count
        except Exception as e:
            logger.error(f"减少点赞数量失败: {e}")
            return None

    async def is_liked_by_user(
        self, user_id: int, content_type: str, content_id: int
    ) -> bool | None:
        """检查用户是否已点赞"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_like_key(user_id, content_type, content_id)
            result = await self.redis_client.get(key)
            return result == b"1" if result is not None else None
        except Exception as e:
            logger.error(f"检查用户点赞状态失败: {e}")
            return None

    async def set_user_like_status(
        self, user_id: int, content_type: str, content_id: int, is_liked: bool
    ) -> bool:
        """设置用户点赞状态"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_like_key(user_id, content_type, content_id)
            value = "1" if is_liked else "0"
            await self.redis_client.setex(key, self.cache_expire, value)

            # 同时更新用户点赞集合
            set_key = self._get_user_likes_set_key(user_id, content_type)
            if is_liked:
                await self.redis_client.sadd(set_key, content_id)
            else:
                await self.redis_client.srem(set_key, content_id)
            await self.redis_client.expire(set_key, self.cache_expire)

            return True
        except Exception as e:
            logger.error(f"设置用户点赞状态失败: {e}")
            return False

    async def get_user_liked_contents(self, user_id: int, content_type: str) -> set[int] | None:
        """获取用户点赞的内容ID集合"""
        try:
            await self._ensure_redis_client()
            key = self._get_user_likes_set_key(user_id, content_type)
            content_ids = await self.redis_client.smembers(key)
            return {int(cid) for cid in content_ids} if content_ids else set()
        except Exception as e:
            logger.error(f"获取用户点赞内容失败: {e}")
            return None

    async def batch_get_like_info(
        self, content_items: list[tuple[str, int]], user_id: int | None = None
    ) -> dict[tuple[str, int], dict[str, Any]]:
        """批量获取点赞信息"""
        result = {}
        try:
            await self._ensure_redis_client()
            # 批量获取点赞数量
            pipe = await self.redis_client.pipeline()
            for content_type, content_id in content_items:
                key = self._get_like_count_key(content_type, content_id)
                pipe.get(key)

            like_counts = await pipe.execute()

            # 如果需要获取用户点赞状态
            user_like_statuses = []
            if user_id:
                pipe = await self.redis_client.pipeline()
                for content_type, content_id in content_items:
                    key = self._get_user_like_key(user_id, content_type, content_id)
                    pipe.get(key)
                user_like_statuses = await pipe.execute()

            # 组装结果
            for i, (content_type, content_id) in enumerate(content_items):
                like_count = like_counts[i]
                like_count = int(like_count) if like_count is not None else None

                is_liked = False
                if user_id and i < len(user_like_statuses):
                    status = user_like_statuses[i]
                    is_liked = status == b"1" if status is not None else None

                result[(content_type, content_id)] = {
                    "like_count": like_count if like_count is not None else 0,
                    "is_liked": is_liked if is_liked is not None else False,
                }

        except Exception as e:
            logger.error(f"批量获取点赞信息失败: {e}")

        return result

    async def update_hot_content(
        self, content_type: str, content_rankings: list[dict[str, Any]]
    ) -> bool:
        """更新热点内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_content_key(content_type)
            data = json.dumps(content_rankings, ensure_ascii=False)
            await self.redis_client.setex(key, self.hot_cache_expire, data)
            return True
        except Exception as e:
            logger.error(f"更新热点内容失败: {e}")
            return False

    async def get_hot_content(self, content_type: str) -> list[dict[str, Any]] | None:
        """获取热点内容排行"""
        try:
            await self._ensure_redis_client()
            key = self._get_hot_content_key(content_type)
            data = await self.redis_client.get(key)
            if data:
                return json.loads(data.decode("utf-8"))
            return None
        except Exception as e:
            logger.error(f"获取热点内容失败: {e}")
            return None

    async def clear_content_cache(self, content_type: str, content_id: int) -> bool:
        """清除特定内容的缓存"""
        try:
            await self._ensure_redis_client()
            # 清除点赞数量缓存
            like_count_key = self._get_like_count_key(content_type, content_id)
            await self.redis_client.delete(like_count_key)

            # 清除相关的用户点赞状态缓存（这里只能清除已知的，实际使用中可能需要其他策略）
            # 注意：这里无法清除所有用户的点赞状态缓存，因为不知道所有用户ID
            # 在实际应用中，可以考虑使用缓存标签或其他策略

            return True
        except Exception as e:
            logger.error(f"清除内容缓存失败: {e}")
            return False

    async def clear_user_cache(self, user_id: int) -> bool:
        """清除用户相关缓存"""
        try:
            await self._ensure_redis_client()
            # 清除用户点赞集合缓存
            for content_type in ["article", "video"]:
                set_key = self._get_user_likes_set_key(user_id, content_type)
                await self.redis_client.delete(set_key)

            return True
        except Exception as e:
            logger.error(f"清除用户缓存失败: {e}")
            return False


# 创建全局实例
like_cache_service = LikeCacheService()
