"""视频权限服务类

专门处理视频权限逻辑的服务类，包含各种权限检查方法
"""

from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models
from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    ResourceType,
    Role,
    Scope,
)


class VideoPermissionService:
    """视频权限服务类"""

    @staticmethod
    async def get_accessible_videos_by_category(
        db: AsyncSession,
        category_id: int,
        current_user: models.User | None = None,
        skip: int = 0,
        limit: int = 100,
        show_draft: bool = False,
        show_pending: bool = False,
    ):
        """
        获取用户可访问的视频列表

        Args:
            db: 数据库会话
            category_id: 分类ID
            current_user: 当前用户（可为None，表示游客）
            skip: 跳过的记录数
            limit: 返回的最大记录数
            show_draft: 是否显示草稿
            show_pending: 是否显示待审核

        Returns:
            视频列表和总数
        """
        user_role = current_user.role if current_user else Role.GUEST

        # 基础查询
        query = (
            select(models.Video)
            .where(models.Video.category_id == category_id)
            .options(
                selectinload(models.Video.tags),
                selectinload(models.Video.author),
            )
        )

        conditions = []

        if user_role in [Role.SUPER_ADMIN, Role.ADMIN]:
            # 管理员可以看到所有视频
            pass  # 不添加任何条件
        elif user_role == Role.USER:
            # 普通用户的权限逻辑
            # 1. 已发布且已审核的视频（公开视频）
            public_condition = and_(
                models.Video.is_published == True, models.Video.is_approved == True
            )
            conditions.append(public_condition)

            # 2. 自己的视频（包括草稿和待审核）
            if current_user:
                own_videos_condition = models.Video.author_id == current_user.id
                conditions.append(own_videos_condition)

            # 3. 根据参数决定是否包含特定状态的视频
            if show_draft and current_user:
                # 显示自己的草稿
                draft_condition = and_(
                    models.Video.is_published == False,
                    models.Video.author_id == current_user.id,
                )
                conditions.append(draft_condition)

            if show_pending and current_user:
                # 显示自己的待审核视频
                pending_condition = and_(
                    models.Video.is_published == True,
                    models.Video.is_approved == False,
                    models.Video.author_id == current_user.id,
                )
                conditions.append(pending_condition)

        else:  # Role.GUEST
            # 游客只能看到已发布且已审核的视频
            public_condition = and_(
                models.Video.is_published == True, models.Video.is_approved == True
            )
            conditions.append(public_condition)

        # 应用条件
        if conditions:
            if len(conditions) == 1:
                query = query.where(conditions[0])
            else:
                query = query.where(or_(*conditions))

        # 获取总数
        count_query = select(models.Video).where(models.Video.category_id == category_id)
        if conditions:
            if len(conditions) == 1:
                count_query = count_query.where(conditions[0])
            else:
                count_query = count_query.where(or_(*conditions))

        from sqlalchemy import func

        count_query = count_query.with_only_columns(func.count().label("count"))
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        videos = result.scalars().all()

        return videos, total

    @staticmethod
    async def check_video_access(
        db: AsyncSession, content: models.Video, current_user: models.User | None = None
    ) -> bool:
        """
        检查视频访问权限
        """
        if not content:
            return False

        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.READ, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.READ, Scope.OWN)
            ):
                return True

        if content.is_published and content.is_approved:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.READ, Scope.PUBLIC)
            ):
                return True

        return False

    @staticmethod
    async def check_video_update_permission(
        db: AsyncSession, content: models.Video, current_user: models.User
    ) -> bool:
        """
        检查视频更新权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.UPDATE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.UPDATE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def check_video_delete_permission(
        db: AsyncSession, content: models.Video, current_user: models.User
    ) -> bool:
        """
        检查视频删除权限
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.DELETE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            if await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.DELETE, Scope.OWN)
            ):
                return True

        return False

    @staticmethod
    async def check_video_move_permission(
        db: AsyncSession, content: models.Video, folder_id: int, current_user: models.User
    ) -> bool:
        """
        检查视频移动权限
        """
        # 移动权限复用更新权限
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.UPDATE, Scope.ALL)
        ):
            return True

        if current_user and content.author_id == current_user.id:
            # 检查目标文件夹是否属于当前用户
            if folder_id:
                folder = await db.get(models.VideoFolder, folder_id)
                if not folder or folder.user_id != current_user.id:
                    return False
            return await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.UPDATE, Scope.OWN)
            )

        return False

    @staticmethod
    async def can_create_video(db: AsyncSession, current_user: models.User | None) -> bool:
        """
        检查用户是否可以创建视频
        """
        if not current_user:
            return False

        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.CREATE, Scope.OWN)
        )

    @staticmethod
    async def can_publish_video(
        db: AsyncSession, current_user: models.User, video: models.Video
    ) -> bool:
        """
        检查用户是否可以发布视频
        """
        if await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.PUBLISH, Scope.ALL)
        ):
            return True

        if video.author_id == current_user.id:
            return await PermissionChecker.check_permission(
                db, current_user, Permission(ResourceType.VIDEO, Action.PUBLISH, Scope.OWN)
            )

        return False

    @staticmethod
    async def can_approve_video(db: AsyncSession, current_user: models.User) -> bool:
        """
        检查用户是否可以审核视频
        """
        return await PermissionChecker.check_permission(
            db, current_user, Permission(ResourceType.VIDEO, Action.APPROVE, Scope.ALL)
        )


# 创建服务实例
video_permission_service = VideoPermissionService()
