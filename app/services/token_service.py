"""Token 管理服务"""

import json
from datetime import datetime, timedelta

from jose import jwt

from app.core.config import settings
from app.db.redis import get_redis
from app.services.logger import get_logger

logger = get_logger(__name__)


class TokenService:
    """Token 管理服务"""

    @staticmethod
    def _get_token_key(token: str) -> str:
        """获取 token 在 Redis 中的键"""
        return f"token:{token}"

    @staticmethod
    def _get_user_tokens_key(username: str) -> str:
        """获取用户所有 token 在 Redis 中的键"""
        return f"user_tokens:{username}"

    @staticmethod
    def _get_device_token_key(username: str, device_id: int) -> str:
        """获取设备 token 在 Redis 中的键"""
        return f"device_token:{username}:{device_id}"

    @staticmethod
    async def create_token(
        username: str,
        device_id: int | None = None,
        expires_delta: timedelta | None = None,
        token_type: str = "access",
    ) -> str:
        """创建并存储 token

        Args:
            username: 用户名
            device_id: 设备ID（可选）
            expires_delta: 过期时间增量
            token_type: token 类型

        Returns:
            str: 生成的 token
        """
        try:
            # 创建 JWT token
            to_encode = {"sub": username, "type": token_type}

            if device_id:
                to_encode["device_id"] = device_id

            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

            to_encode["exp"] = expire
            token = jwt.encode(to_encode, settings.SECRET_KEY, algorithm="HS256")

            # 存储到 Redis
            redis_client = await get_redis()

            # Token 信息
            token_info = {
                "username": username,
                "device_id": device_id,
                "token_type": token_type,
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": expire.isoformat(),
            }

            # 计算过期时间（秒）
            expire_seconds = (
                int(expires_delta.total_seconds())
                if expires_delta
                else settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            )

            # 存储 token 信息
            await redis_client.setex(
                TokenService._get_token_key(token), expire_seconds, json.dumps(token_info)
            )

            # 将 token 添加到用户的 token 列表
            user_tokens_key = TokenService._get_user_tokens_key(username)
            await redis_client.sadd(user_tokens_key, token)
            await redis_client.expire(user_tokens_key, expire_seconds)

            # 如果有设备ID，存储设备对应的 token
            if device_id:
                device_token_key = TokenService._get_device_token_key(username, device_id)
                # 先删除该设备的旧 token
                old_token = await redis_client.get(device_token_key)
                if old_token:
                    await TokenService.revoke_token(old_token)

                # 存储新 token
                await redis_client.setex(device_token_key, expire_seconds, token)

            logger.info(f"Token 创建成功，用户：{username}，设备：{device_id}")
            return token

        except Exception as e:
            logger.error(f"创建 token 失败：{str(e)}")
            raise

    @staticmethod
    async def verify_token(token: str) -> dict | None:
        """验证 token 是否有效

        Args:
            token: JWT token

        Returns:
            Dict: token 信息，如果无效返回 None
        """
        try:
            redis_client = await get_redis()

            # 检查 Redis 中是否存在该 token
            token_info_str = await redis_client.get(TokenService._get_token_key(token))
            if not token_info_str:
                return None

            # 验证 JWT token
            try:
                payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            except jwt.ExpiredSignatureError:
                # Token 过期，从 Redis 中删除
                await TokenService.revoke_token(token)
                return None
            except jwt.JWTError:
                # Token 无效，从 Redis 中删除
                await TokenService.revoke_token(token)
                return None

            # 解析 token 信息
            token_info = json.loads(token_info_str)
            token_info.update(payload)

            return token_info

        except Exception as e:
            logger.error(f"验证 token 失败：{str(e)}")
            return None

    @staticmethod
    async def revoke_token(token: str) -> bool:
        """撤销 token

        Args:
            token: 要撤销的 token

        Returns:
            bool: 是否成功撤销
        """
        try:
            redis_client = await get_redis()

            # 获取 token 信息
            token_info_str = await redis_client.get(TokenService._get_token_key(token))
            if not token_info_str:
                return False

            token_info = json.loads(token_info_str)
            username = token_info.get("username")
            device_id = token_info.get("device_id")

            # 删除 token
            await redis_client.delete(TokenService._get_token_key(token))

            # 从用户 token 列表中移除
            if username:
                await redis_client.srem(TokenService._get_user_tokens_key(username), token)

            # 如果有设备ID，删除设备 token 映射
            if username and device_id:
                device_token_key = TokenService._get_device_token_key(username, device_id)
                current_token = await redis_client.get(device_token_key)
                if current_token == token:
                    await redis_client.delete(device_token_key)

            logger.info(f"Token 撤销成功，用户：{username}，设备：{device_id}")
            return True

        except Exception as e:
            logger.error(f"撤销 token 失败：{str(e)}")
            return False

    @staticmethod
    async def revoke_user_tokens(username: str) -> int:
        """撤销用户的所有 token

        Args:
            username: 用户名

        Returns:
            int: 撤销的 token 数量
        """
        try:
            redis_client = await get_redis()

            # 获取用户所有 token
            user_tokens_key = TokenService._get_user_tokens_key(username)
            tokens = await redis_client.smembers(user_tokens_key)

            revoked_count = 0
            for token in tokens:
                if await TokenService.revoke_token(token):
                    revoked_count += 1

            # 删除用户 token 列表
            await redis_client.delete(user_tokens_key)

            logger.info(f"用户 {username} 的所有 token 已撤销，共 {revoked_count} 个")
            return revoked_count

        except Exception as e:
            logger.error(f"撤销用户 token 失败：{str(e)}")
            return 0

    @staticmethod
    async def revoke_device_token(username: str, device_id: int) -> bool:
        """撤销设备的 token

        Args:
            username: 用户名
            device_id: 设备ID

        Returns:
            bool: 是否成功撤销
        """
        try:
            redis_client = await get_redis()

            # 获取设备 token
            device_token_key = TokenService._get_device_token_key(username, device_id)
            token = await redis_client.get(device_token_key)

            if token:
                return await TokenService.revoke_token(token)

            return False

        except Exception as e:
            logger.error(f"撤销设备 token 失败：{str(e)}")
            return False

    @staticmethod
    async def get_user_active_tokens(username: str) -> list[dict]:
        """获取用户的活跃 token 列表

        Args:
            username: 用户名

        Returns:
            List[Dict]: 活跃的 token 信息列表
        """
        try:
            redis_client = await get_redis()

            # 获取用户所有 token
            user_tokens_key = TokenService._get_user_tokens_key(username)
            tokens = await redis_client.smembers(user_tokens_key)

            active_tokens = []
            for token in tokens:
                token_info = await TokenService.verify_token(token)
                if token_info:
                    active_tokens.append(token_info)

            return active_tokens

        except Exception as e:
            logger.error(f"获取用户活跃 token 失败：{str(e)}")
            return []
