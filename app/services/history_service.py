"""历史记录服务"""

from sqlalchemy.ext.asyncio import AsyncSession

from app import crud
from app.services.history_cache_service import history_service
from app.services.logger import get_logger
from app.services.visit_cache_service import visit_cache_service

logger = get_logger(__name__)


async def record_user_history(
    db: AsyncSession, user_id: int, content_type: str, content_id: int
) -> bool:
    """记录用户浏览历史（同时更新数据库和缓存）"""
    logger.info(f"记录用户历史: {user_id}, {content_type}, {content_id}")
    try:
        # 更新数据库
        history = await crud.history.get_by_user_and_content(
            db, user_id=user_id, content_type=content_type, content_id=content_id
        )
        if not history:
            await crud.history.create(
                db=db,
                obj_in={
                    "user_id": user_id,
                    "content_type": content_type,
                    "content_id": content_id,
                },
            )
        else:
            await crud.history.update_visit(db=db, db_obj=history)

        # 更新历史缓存
        await history_service.set_history(
            user_id=user_id, content_type=content_type, content_id=content_id
        )
        logger.info(f"历史记录缓存更新成功: {user_id}, {content_type}, {content_id}")

        # 更新访问次数缓存
        await visit_cache_service.increment_visit_count(content_type, content_id)
        logger.info(f"访问次数缓存更新成功: {content_type}, {content_id}")

        return True
    except Exception as e:
        logger.error(f"记录用户历史失败: {e}")
        return False
