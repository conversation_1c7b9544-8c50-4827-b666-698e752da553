"""推荐效果评估服务模块"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models

logger = logging.getLogger(__name__)


class RecommendationEvaluationService:
    """推荐效果评估服务类"""

    def __init__(self):
        pass

    async def calculate_click_through_rate(
        self,
        db: AsyncSession,
        algorithm_type: str | None = None,
        days: int = 7,
        position: str | None = None,
    ) -> dict[str, float]:
        """计算点击率（CTR）"""
        try:
            # 构建查询条件
            query_conditions = [
                models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days)
            ]

            if algorithm_type:
                query_conditions.append(models.RecommendationLog.algorithm_type == algorithm_type)

            if position:
                query_conditions.append(models.RecommendationLog.position == position)

            # 获取总推荐数
            total_recommendations_query = select(func.count(models.RecommendationLog.id)).where(
                and_(*query_conditions)
            )
            total_recommendations = (await db.execute(total_recommendations_query)).scalar_one()

            # 获取点击数
            clicked_recommendations_query = select(func.count(models.RecommendationLog.id)).where(
                and_(*query_conditions, models.RecommendationLog.is_clicked == True)
            )
            clicked_recommendations = (await db.execute(clicked_recommendations_query)).scalar_one()

            # 计算CTR
            ctr = (
                clicked_recommendations / total_recommendations
                if total_recommendations > 0
                else 0.0
            )

            return {
                "total_recommendations": total_recommendations,
                "clicked_recommendations": clicked_recommendations,
                "click_through_rate": ctr,
                "algorithm_type": algorithm_type or "all",
                "days": days,
                "position": position or "all",
            }

        except Exception as e:
            logger.error(f"计算点击率失败: {e}")
            return {"click_through_rate": 0.0, "error": str(e)}

    async def calculate_precision_recall(
        self,
        db: AsyncSession,
        user_id: int,
        algorithm_type: str,
        days: int = 7,
        k: int = 10,
    ) -> dict[str, float]:
        """计算精确率和召回率"""
        try:
            # 获取用户在指定时间内的推荐记录
            recommendations_query = (
                select(models.RecommendationLog)
                .where(
                    models.RecommendationLog.user_id == user_id,
                    models.RecommendationLog.algorithm_type == algorithm_type,
                    models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days),
                )
                .order_by(desc(models.RecommendationLog.created_at))
                .limit(k)
            )
            recommendations = (await db.execute(recommendations_query)).scalars().all()

            if not recommendations:
                return {"precision": 0.0, "recall": 0.0, "f1_score": 0.0}

            # 解析推荐内容
            recommended_items: set[tuple[str, int]] = set()
            for rec in recommendations:
                items = json.loads(rec.recommended_items)
                for item in items[:k]:  # 只考虑前k个推荐
                    recommended_items.add((item["content_type"], item["content_id"]))

            # 获取用户在同一时间段内的实际交互（作为相关内容）
            relevant_interactions_query = select(models.UserInteraction).where(
                models.UserInteraction.user_id == user_id,
                models.UserInteraction.created_at >= datetime.utcnow() - timedelta(days=days),
                models.UserInteraction.interaction_type.in_(
                    ["like", "favorite", "comment", "share"]
                ),
            )
            relevant_interactions = (await db.execute(relevant_interactions_query)).scalars().all()

            relevant_items: set[tuple[str, int]] = set()
            for interaction in relevant_interactions:
                relevant_items.add((interaction.content_type, interaction.content_id))

            # 计算精确率和召回率
            true_positives = len(recommended_items & relevant_items)
            precision = true_positives / len(recommended_items) if recommended_items else 0.0
            recall = true_positives / len(relevant_items) if relevant_items else 0.0

            # 计算F1分数
            f1_score = (
                2 * (precision * recall) / (precision + recall) if precision + recall > 0 else 0.0
            )

            return {
                "precision": precision,
                "recall": recall,
                "f1_score": f1_score,
                "recommended_count": len(recommended_items),
                "relevant_count": len(relevant_items),
                "true_positives": true_positives,
            }

        except Exception as e:
            logger.error(f"计算精确率和召回率失败: {e}")
            return {"precision": 0.0, "recall": 0.0, "f1_score": 0.0, "error": str(e)}

    async def calculate_diversity_score(
        self, db: AsyncSession, algorithm_type: str, days: int = 7
    ) -> dict[str, float]:
        """计算推荐多样性分数"""
        try:
            # 获取指定算法的推荐记录
            recommendations_query = select(models.RecommendationLog).where(
                models.RecommendationLog.algorithm_type == algorithm_type,
                models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days),
            )
            recommendations = (await db.execute(recommendations_query)).scalars().all()

            if not recommendations:
                return {
                    "diversity_score": 0.0,
                    "category_diversity": 0.0,
                    "content_type_diversity": 0.0,
                }

            # 统计推荐内容的分类和类型分布
            categories = set()
            content_types = set()
            total_items = 0

            for rec in recommendations:
                items = json.loads(rec.recommended_items)
                for item in items:
                    total_items += 1
                    content_types.add(item["content_type"])

                    # 获取内容的分类信息
                    if item["content_type"] == "article":
                        content = await crud.article.get(db, id=item["content_id"])
                        if content and content.category:
                            categories.add(content.category.name)
                    elif item["content_type"] == "video":
                        content = await crud.video.get(db, id=item["content_id"])
                        if content and content.category:
                            categories.add(content.category.name)

            # 计算多样性分数
            category_diversity = len(categories) / max(total_items, 1)
            content_type_diversity = len(content_types) / 2  # 假设只有article和video两种类型

            # 综合多样性分数
            diversity_score = (category_diversity + content_type_diversity) / 2

            return {
                "diversity_score": diversity_score,
                "category_diversity": category_diversity,
                "content_type_diversity": content_type_diversity,
                "unique_categories": len(categories),
                "unique_content_types": len(content_types),
                "total_items": total_items,
            }

        except Exception as e:
            logger.error(f"计算多样性分数失败: {e}")
            return {"diversity_score": 0.0, "error": str(e)}

    async def calculate_coverage_score(
        self, db: AsyncSession, algorithm_type: str, days: int = 7
    ) -> dict[str, float]:
        """计算推荐覆盖率"""
        try:
            # 获取所有可推荐的内容数量
            total_articles_query = select(func.count(models.Article.id)).where(
                models.Article.is_published == True
            )
            total_articles = (await db.execute(total_articles_query)).scalar_one()

            total_videos_query = select(func.count(models.Video.id)).where(
                models.Video.is_published == True
            )
            total_videos = (await db.execute(total_videos_query)).scalar_one()
            total_content = total_articles + total_videos

            # 获取被推荐的内容
            recommendations_query = select(models.RecommendationLog).where(
                models.RecommendationLog.algorithm_type == algorithm_type,
                models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days),
            )
            recommendations = (await db.execute(recommendations_query)).scalars().all()

            recommended_content: set[tuple[str, int]] = set()
            for rec in recommendations:
                items = json.loads(rec.recommended_items)
                for item in items:
                    recommended_content.add((item["content_type"], item["content_id"]))

            # 计算覆盖率
            coverage = len(recommended_content) / total_content if total_content > 0 else 0.0

            return {
                "coverage_score": coverage,
                "recommended_unique_items": len(recommended_content),
                "total_available_items": total_content,
                "total_articles": total_articles,
                "total_videos": total_videos,
            }

        except Exception as e:
            logger.error(f"计算覆盖率失败: {e}")
            return {"coverage_score": 0.0, "error": str(e)}

    async def calculate_novelty_score(
        self, db: AsyncSession, user_id: int, algorithm_type: str, days: int = 7
    ) -> dict[str, float]:
        """计算推荐新颖性分数"""
        try:
            # 获取用户历史交互内容
            historical_interactions_query = select(models.UserInteraction).where(
                models.UserInteraction.user_id == user_id,
                models.UserInteraction.created_at < datetime.utcnow() - timedelta(days=days),
            )
            historical_interactions = (
                (await db.execute(historical_interactions_query)).scalars().all()
            )

            historical_content: set[tuple[str, int]] = set()
            for interaction in historical_interactions:
                historical_content.add((interaction.content_type, interaction.content_id))

            # 获取推荐内容
            recommendations_query = select(models.RecommendationLog).where(
                models.RecommendationLog.user_id == user_id,
                models.RecommendationLog.algorithm_type == algorithm_type,
                models.RecommendationLog.created_at >= datetime.utcnow() - timedelta(days=days),
            )
            recommendations = (await db.execute(recommendations_query)).scalars().all()

            recommended_content: set[tuple[str, int]] = set()
            for rec in recommendations:
                items = json.loads(rec.recommended_items)
                for item in items:
                    recommended_content.add((item["content_type"], item["content_id"]))

            # 计算新颖性（推荐内容中用户未接触过的比例）
            if not recommended_content:
                novelty = 0.0
            else:
                novel_items = recommended_content - historical_content
                novelty = len(novel_items) / len(recommended_content)

            return {
                "novelty_score": novelty,
                "recommended_items": len(recommended_content),
                "novel_items": len(novel_items),
                "historical_items": len(historical_content),
            }

        except Exception as e:
            logger.error(f"计算新颖性分数失败: {e}")
            return {"novelty_score": 0.0, "error": str(e)}

    async def generate_comprehensive_report(
        self, db: AsyncSession, days: int = 7
    ) -> dict[str, Any]:
        """生成综合评估报告"""
        try:
            report = {
                "evaluation_period": f"{days} days",
                "generated_at": datetime.utcnow().isoformat(),
                "algorithms": {},
                "overall_metrics": {},
            }

            # 评估各个算法
            algorithms = ["collaborative", "content_based", "hot", "hybrid"]

            for algorithm in algorithms:
                algorithm_metrics = {}

                # CTR
                ctr_data = await self.calculate_click_through_rate(db, algorithm, days)
                algorithm_metrics["click_through_rate"] = ctr_data

                # 多样性
                diversity_data = await self.calculate_diversity_score(db, algorithm, days)
                algorithm_metrics["diversity"] = diversity_data

                # 覆盖率
                coverage_data = await self.calculate_coverage_score(db, algorithm, days)
                algorithm_metrics["coverage"] = coverage_data

                report["algorithms"][algorithm] = algorithm_metrics

            # 计算整体指标
            total_recommendations = sum(
                report["algorithms"][alg]["click_through_rate"]["total_recommendations"]
                for alg in algorithms
            )

            total_clicks = sum(
                report["algorithms"][alg]["click_through_rate"]["clicked_recommendations"]
                for alg in algorithms
            )

            overall_ctr = total_clicks / total_recommendations if total_recommendations > 0 else 0.0

            report["overall_metrics"] = {
                "total_recommendations": total_recommendations,
                "total_clicks": total_clicks,
                "overall_ctr": overall_ctr,
                "best_performing_algorithm": self._find_best_algorithm(report["algorithms"]),
            }

            return report

        except Exception as e:
            logger.error(f"生成综合评估报告失败: {e}")
            return {"error": str(e)}

    def _find_best_algorithm(self, algorithms_data: dict[str, Any]) -> str:
        """找出表现最好的算法"""
        best_algorithm = "hybrid"  # 默认值
        best_score = 0.0

        for algorithm, data in algorithms_data.items():
            # 综合考虑CTR、多样性和覆盖率
            ctr = data.get("click_through_rate", {}).get("click_through_rate", 0.0)
            diversity = data.get("diversity", {}).get("diversity_score", 0.0)
            coverage = data.get("coverage", {}).get("coverage_score", 0.0)

            # 加权综合分数
            composite_score = ctr * 0.5 + diversity * 0.3 + coverage * 0.2

            if composite_score > best_score:
                best_score = composite_score
                best_algorithm = algorithm

        return best_algorithm


# 创建全局推荐评估服务实例
recommendation_evaluation_service = RecommendationEvaluationService()
