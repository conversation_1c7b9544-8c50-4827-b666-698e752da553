import enum
from datetime import datetime

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class CommentType(str, enum.Enum):
    """评论类型枚举"""

    ARTICLE = "article"  # 文章评论
    VIDEO = "video"  # 视频评论


class Comment(Base):
    """评论数据模型"""

    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text, nullable=False)
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 评论类型
    comment_type = Column(Enum("article", "video", name="comment_type"), nullable=False, index=True)
    # 关联ID
    article_id = Column(Integer, ForeignKey("articles.id"), index=True, nullable=True)
    video_id = Column(Integer, ForeignKey("videos.id"), index=True, nullable=True)

    # 新增：用于评论回复的自引用外键
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    # 回复目标评论ID（具体回复的是哪条评论）
    reply_to_id = Column(Integer, ForeignKey("comments.id"), nullable=True)

    # 是否显示
    is_visible = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    author = relationship("User", foreign_keys=[author_id], back_populates="comments")
    reply_to_comment = relationship("Comment", foreign_keys=[reply_to_id], remote_side=[id])
    article = relationship("Article", back_populates="comments")
    video = relationship("Video", back_populates="comments")

    # 新增：自引用关系，用于获取回复
    parent = relationship(
        "Comment", remote_side=[id], back_populates="replies", foreign_keys=[parent_id]
    )
    replies = relationship(
        "Comment", back_populates="parent", cascade="all, delete-orphan", foreign_keys=[parent_id]
    )

    def __repr__(self):
        return f"<Comment {self.id}>"
