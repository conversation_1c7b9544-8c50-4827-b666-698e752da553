"""文件哈希表"""

from datetime import datetime

from sqlalchemy import (
    Column,
    DateTime,
    Integer,
    String,
    UniqueConstraint,
)

from app.db.session import Base


class FileHash(Base):
    __tablename__ = "file_hashes"

    id = Column(Integer, primary_key=True, index=True)
    # oss 文件路径
    file_path = Column(String(255), nullable=False, index=True)
    file_hash = Column(String(255), nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 联合唯一约束：同一文件路径只能有一个哈希值
    __table_args__ = (UniqueConstraint("file_hash", name="uq_file_path"),)

    def __repr__(self):
        return f"<FileHash {self.file_path}>"
