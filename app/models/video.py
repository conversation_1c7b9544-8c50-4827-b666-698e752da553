from datetime import datetime

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship

from app.db.session import Base


class Video(Base):
    """视频数据模型"""

    __tablename__ = "videos"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), index=True, nullable=False)
    description = Column(Text, nullable=True)
    url = Column(String(512), nullable=False, comment="视频URL")
    cover_url = Column(String(512), nullable=True, comment="封面图URL")
    duration = Column(Integer, nullable=True, comment="视频时长（秒）")
    author_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 是否发布 如果未发布则显示到草稿箱中
    is_published = Column(Boolean, default=False)
    is_approved = Column(Boolean, default=False, comment="是否通过审核 仅在发布后需要审核")
    visit_count = Column(Integer, default=0, comment="访问次数")
    # 排序权重
    sort_order = Column(Integer, default=0, comment="排序权重，值越大越靠前")
    # 软删除标记
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    # 分类
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    # 所属文件夹 - 不再允许为空，每个视频必须属于一个文件夹
    folder_id = Column(Integer, ForeignKey("video_folders.id"), nullable=False, index=True)

    # 关联关系
    author = relationship("User", back_populates="videos")
    comments = relationship("Comment", back_populates="video")
    category = relationship("Category", back_populates="videos")
    tags = relationship("Tag", secondary="video_tags", back_populates="videos")
    folder = relationship("VideoFolder", back_populates="videos")

    def __repr__(self):
        return f"<Video {self.title}>"
