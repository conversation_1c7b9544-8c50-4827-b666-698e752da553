"""测试422错误处理"""

import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)


class Test422ErrorHandling:
    """422错误处理测试"""

    def test_missing_required_field(self):
        """测试缺少必需字段"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={"username": "test"}  # 缺少email和age
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        assert data["message"] == "请求数据验证失败"
        assert "errors" in data
        
        # 检查错误详情
        errors = data["errors"]
        assert len(errors) >= 2  # 至少缺少email和age
        
        error_fields = [error["field"] for error in errors]
        assert "email" in error_fields
        assert "age" in error_fields

    def test_type_error(self):
        """测试字段类型错误"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={
                "username": "test",
                "email": "<EMAIL>",
                "age": "not_a_number"  # 应该是整数
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        
        # 检查是否有age字段的类型错误
        age_errors = [error for error in data["errors"] if "age" in error["field"]]
        assert len(age_errors) > 0
        assert "type_error" in age_errors[0]["type"]

    def test_validation_error(self):
        """测试字段值验证失败"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={
                "username": "test",
                "email": "<EMAIL>",
                "age": 15  # 小于最小值18
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        
        # 检查age字段的验证错误
        age_errors = [error for error in data["errors"] if "age" in error["field"]]
        assert len(age_errors) > 0

    def test_enum_error(self):
        """测试枚举值错误"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={
                "username": "test",
                "email": "<EMAIL>",
                "age": 25,
                "role": "invalid_role"  # 无效的枚举值
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        
        # 检查role字段的枚举错误
        role_errors = [error for error in data["errors"] if "role" in error["field"]]
        assert len(role_errors) > 0

    def test_custom_validator_error(self):
        """测试自定义验证器失败"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={
                "username": "test@#$",  # 包含特殊字符，违反自定义验证器
                "email": "<EMAIL>",
                "age": 25
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        
        # 检查username字段的自定义验证错误
        username_errors = [error for error in data["errors"] if "username" in error["field"]]
        assert len(username_errors) > 0

    def test_query_parameter_validation(self):
        """测试查询参数验证"""
        response = client.get(
            "/api/v1/validation/search/demo",
            params={
                "q": "",  # 空字符串，违反min_length=1
                "page": 0,  # 小于最小值1
                "size": 101,  # 大于最大值100
                "sort_by": "invalid_field",  # 不匹配正则表达式
                "order": "invalid_order"  # 不匹配正则表达式
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        assert len(data["errors"]) >= 5  # 所有参数都有错误

    def test_successful_validation(self):
        """测试成功的验证"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={
                "username": "testuser",
                "email": "<EMAIL>",
                "age": 25,
                "role": "user",
                "tags": ["tag1", "tag2"],
                "bio": "这是一个测试用户",
                "is_active": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "user" in data

    def test_product_validation(self):
        """测试产品验证"""
        # 测试价格为负数的情况
        response = client.post(
            "/api/v1/validation/products/demo",
            json={
                "name": "测试产品",
                "price": -10.0,  # 负价格
                "category_id": 0,  # 小于最小值1
                "description": "测试描述"
            }
        )
        
        assert response.status_code == 422
        data = response.json()
        assert data["status"] == "error"
        
        # 检查price和category_id的错误
        error_fields = [error["field"] for error in data["errors"]]
        assert "price" in error_fields
        assert "category_id" in error_fields

    def test_phone_validation(self):
        """测试手机号码验证"""
        # 测试无效手机号
        response = client.post(
            "/api/v1/validation/phone/demo",
            json="12345"  # 无效手机号
        )
        
        assert response.status_code == 422

    def test_complex_validation(self):
        """测试复杂数据验证"""
        # 测试缺少user字段
        response = client.post(
            "/api/v1/validation/complex/demo",
            json={
                "products": [],
                "metadata": {}
            }
        )
        
        assert response.status_code == 422

    def test_error_response_format(self):
        """测试错误响应格式"""
        response = client.post(
            "/api/v1/validation/users/demo",
            json={}  # 空对象，缺少所有必需字段
        )
        
        assert response.status_code == 422
        data = response.json()
        
        # 检查响应格式
        assert "status" in data
        assert "message" in data
        assert "errors" in data
        assert "detail" in data
        
        assert data["status"] == "error"
        assert data["message"] == "请求数据验证失败"
        assert data["detail"] == "请检查请求参数格式和类型"
        
        # 检查错误详情格式
        for error in data["errors"]:
            assert "field" in error
            assert "message" in error
            assert "type" in error

    def test_get_error_examples(self):
        """测试获取错误示例"""
        response = client.get("/api/v1/validation/error-examples")
        
        assert response.status_code == 200
        data = response.json()
        assert "examples" in data
        
        examples = data["examples"]
        assert "missing_field" in examples
        assert "type_error" in examples
        assert "validation_error" in examples
        assert "enum_error" in examples
        assert "custom_validator_error" in examples

    def test_http_exception_handler(self):
        """测试HTTP异常处理器"""
        # 这里可以测试其他HTTP异常，比如404
        response = client.get("/api/v1/validation/nonexistent")
        
        assert response.status_code == 404
        data = response.json()
        assert data["status"] == "error"
        assert "message" in data
        assert "status_code" in data
        assert data["status_code"] == 404


if __name__ == "__main__":
    pytest.main([__file__])
