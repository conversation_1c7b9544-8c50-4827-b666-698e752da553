#!/usr/bin/env python3
"""
测试微信登录响应格式修复
"""

import json
from datetime import datetime

from app.schemas.unified_auth import UnifiedUserInfo
from app.schemas.wechat import LoginStatusResponse


def test_login_status_response():
    """测试LoginStatusResponse模型"""
    print("=== 测试 LoginStatusResponse 模型 ===")
    
    # 创建用户信息
    user_info = UnifiedUserInfo(
        id=8,
        username="17604840253",
        nickname="快乐的土豆196",
        email=None,
        avatar=None,
        is_active=True,
        last_login=datetime.now(),
        created_at=datetime.now(),
        wechat_nickname="",
        wechat_avatar="",
        login_type="wechat"
    )
    
    # 创建登录状态响应
    response = LoginStatusResponse(
        status="confirmed",
        message="登录成功",
        user=user_info,
        access_token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token",
        token_type="bearer"
    )
    
    # 转换为字典并打印
    response_dict = response.model_dump()
    print("修复后的响应格式:")
    print(json.dumps(response_dict, ensure_ascii=False, indent=2, default=str))
    
    # 验证字段
    print("\n=== 字段验证 ===")
    print(f"✅ status: {response_dict.get('status')}")
    print(f"✅ message: {response_dict.get('message')}")
    print(f"✅ user: {'存在' if response_dict.get('user') else '不存在'}")
    print(f"✅ access_token: {'存在' if response_dict.get('access_token') else '不存在'}")
    print(f"✅ token_type: {response_dict.get('token_type')}")
    print(f"❌ user_info: {'存在（不应该存在）' if 'user_info' in response_dict else '不存在（正确）'}")
    
    return response_dict


def test_need_register_response():
    """测试需要注册的响应"""
    print("\n=== 测试需要注册的响应 ===")
    
    response = LoginStatusResponse(
        status="need_register",
        message="用户未注册，需要先注册"
    )
    
    response_dict = response.model_dump()
    print("需要注册的响应格式:")
    print(json.dumps(response_dict, ensure_ascii=False, indent=2, default=str))
    
    return response_dict


def compare_with_original_format():
    """与原始格式对比"""
    print("\n=== 与原始格式对比 ===")
    
    # 原始格式（有问题的）
    original_format = {
        "status": "confirmed",
        "message": "登录成功",
        "user": {
            "id": 8,
            "username": "17604840253",
            "nickname": "快乐的土豆196",
            "email": None,
            "avatar": None,
            "is_active": True,
            "last_login": "2025-07-08T07:55:55.246143",
            "created_at": "2025-07-07T09:11:01.813440",
            "wechat_nickname": "",
            "wechat_avatar": "",
            "login_type": "wechat"
        },
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ljGC9nCujXsgOtpjKU6lnJDNwNjwjPQOrMwIsf2CBu0",
        "token_type": "bearer",
        "user_info": None  # 这个字段是多余的
    }
    
    print("原始格式（有问题）:")
    print(json.dumps(original_format, ensure_ascii=False, indent=2))
    
    print("\n问题分析:")
    print("❌ 同时包含 'user' 和 'user_info' 字段")
    print("❌ 'user_info' 字段为 null，造成混淆")
    print("❌ 响应格式不一致")
    
    # 修复后的格式
    fixed_response = test_login_status_response()
    
    print("\n修复后的优势:")
    print("✅ 只包含 'user' 字段，格式统一")
    print("✅ 移除了多余的 'user_info' 字段")
    print("✅ 使用 UnifiedUserInfo 模型，类型安全")
    print("✅ 与其他认证接口响应格式一致")


def test_model_validation():
    """测试模型验证"""
    print("\n=== 测试模型验证 ===")
    
    try:
        # 测试必需字段
        response = LoginStatusResponse(status="confirmed")
        print("✅ 只提供必需字段 'status' - 成功")
        
        # 测试完整字段
        user_info = UnifiedUserInfo(
            id=1,
            username="test",
            nickname="测试用户",
            is_active=True,
            created_at=datetime.now()
        )
        
        response = LoginStatusResponse(
            status="confirmed",
            message="登录成功",
            user=user_info,
            access_token="test_token",
            token_type="bearer"
        )
        print("✅ 提供完整字段 - 成功")
        
        # 验证字段类型
        assert response.status == "confirmed"
        assert response.message == "登录成功"
        assert response.user.username == "test"
        assert response.access_token == "test_token"
        assert response.token_type == "bearer"
        print("✅ 字段类型验证 - 成功")
        
    except Exception as e:
        print(f"❌ 模型验证失败: {e}")


def main():
    """主函数"""
    print("微信登录响应格式修复测试")
    print("=" * 50)
    
    # 测试修复后的响应格式
    test_login_status_response()
    
    # 测试需要注册的响应
    test_need_register_response()
    
    # 与原始格式对比
    compare_with_original_format()
    
    # 测试模型验证
    test_model_validation()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n总结:")
    print("✅ 移除了多余的 'user_info' 字段")
    print("✅ 统一使用 'user' 字段返回用户信息")
    print("✅ 使用 UnifiedUserInfo 模型确保类型安全")
    print("✅ 响应格式与其他认证接口保持一致")


if __name__ == "__main__":
    main()
