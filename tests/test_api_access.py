#!/usr/bin/env python

import asyncio

from sqlalchemy import select

from app.api.endpoints.auth import create_access_token, get_password_hash
from app.db.session import SessionLocal
from app.models.user import User


async def create_test_user_and_token():
    """创建测试用户并生成访问令牌"""
    print("创建测试用户并生成访问令牌...")
    
    async with SessionLocal() as session:
        # 检查是否已存在测试用户
        result = await session.execute(select(User).filter(User.email == "<EMAIL>"))
        user = result.scalars().first()
        
        if not user:
            print("创建测试用户...")
            user = User(
                username="<EMAIL>",
                email="<EMAIL>",
                password=get_password_hash("testpassword"),
                nickname="Test User",
                is_superuser=True,
            )
            session.add(user)
            await session.commit()
            await session.refresh(user)
        else:
            print(f"测试用户已存在: {user.email}")
        
        # 生成访问令牌
        access_token = await create_access_token(data={"sub": user.email})
        print(f"访问令牌: {access_token}")
        
        return access_token

if __name__ == "__main__":
    token = asyncio.run(create_test_user_and_token())
    print("\n使用以下命令测试API:")
    print(f"curl -X GET http://localhost:8000/api/v1/video-folders/tree -H 'Authorization: Bearer {token}'")