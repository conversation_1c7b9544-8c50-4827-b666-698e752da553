"""统一认证返回值测试"""

from datetime import datetime
from unittest.mock import Mock

import pytest

from app.api.endpoints.sms_auth import AuthResponse
from app.models.user import User
from app.schemas.unified_auth import (
    UnifiedAuthResponse,
    UnifiedAuthResult,
    UnifiedDeviceVerificationResponse,
    UnifiedUserInfo,
)
from app.schemas.wechat import UserInfo as WeChatUserInfo
from app.schemas.wechat import WeChatBindResponse
from app.services.sms_auth_service import AuthenticationResult
from app.services.unified_auth_adapter import unified_auth_adapter


class TestUnifiedAuthResponse:
    """统一认证响应测试"""

    def test_unified_user_info_creation(self):
        """测试统一用户信息创建"""
        user_data = {
            "id": 1,
            "username": "13800138000",
            "nickname": "测试用户",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar.jpg",
            "is_active": True,
            "last_login": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "wechat_nickname": "微信昵称",
            "wechat_avatar": "https://wx.qlogo.cn/avatar.jpg",
            "login_type": "wechat",
        }

        user_info = UnifiedUserInfo.model_validate(user_data)

        assert user_info.id == 1
        assert user_info.username == "13800138000"
        assert user_info.nickname == "测试用户"
        assert user_info.wechat_nickname == "微信昵称"
        assert user_info.login_type == "wechat"

    def test_unified_auth_response_creation(self):
        """测试统一认证响应创建"""
        user_data = {
            "id": 1,
            "username": "13800138000",
            "nickname": "测试用户",
            "email": None,
            "avatar": None,
            "is_active": True,
            "last_login": None,
            "created_at": datetime.utcnow(),
            "wechat_nickname": None,
            "wechat_avatar": None,
            "login_type": "sms",
        }

        response = UnifiedAuthResponse(
            access_token="test_token",
            token_type="bearer",
            user=UnifiedUserInfo.model_validate(user_data),
            message="登录成功",
            auth_method="sms",
        )

        assert response.access_token == "test_token"
        assert response.token_type == "bearer"
        assert response.user.username == "13800138000"
        assert response.message == "登录成功"
        assert response.auth_method == "sms"

    def test_unified_device_verification_response(self):
        """测试统一设备验证响应"""
        response = UnifiedDeviceVerificationResponse(
            requires_device_verification=True,
            message="需要设备验证",
            device_info={"device_type": "mobile", "os": "iOS"},
            verification_token="verify_token_123",
            auth_method="sms",
        )

        assert response.requires_device_verification is True
        assert response.message == "需要设备验证"
        assert response.device_info["device_type"] == "mobile"
        assert response.verification_token == "verify_token_123"
        assert response.auth_method == "sms"


class TestUnifiedAuthAdapter:
    """统一认证适配器测试"""

    def create_mock_user(self) -> User:
        """创建模拟用户对象"""
        user = Mock(spec=User)
        user.id = 1
        user.username = "13800138000"
        user.nickname = "测试用户"
        user.email = "<EMAIL>"
        user.avatar = "https://example.com/avatar.jpg"
        user.is_active = True
        user.last_login = datetime.utcnow()
        user.created_at = datetime.utcnow()
        user.wechat_nickname = None
        user.wechat_avatar = None
        user.login_type = "sms"
        return user

    def test_from_sms_auth_result(self):
        """测试从SMS认证结果转换"""
        user = self.create_mock_user()

        sms_result = AuthenticationResult(
            success=True,
            access_token="test_token",
            user=user,
            requires_device_verification=False,
            message="登录成功",
        )

        unified_result = unified_auth_adapter.from_sms_auth_result(sms_result)

        assert unified_result.success is True
        assert unified_result.access_token == "test_token"
        assert unified_result.auth_method == "sms"
        assert unified_result.requires_device_verification is False

        # 测试转换为认证响应
        auth_response = unified_result.to_auth_response()
        assert isinstance(auth_response, UnifiedAuthResponse)
        assert auth_response.access_token == "test_token"
        assert auth_response.auth_method == "sms"

    def test_from_sms_auth_result_with_device_verification(self):
        """测试需要设备验证的SMS认证结果转换"""
        user = self.create_mock_user()

        sms_result = AuthenticationResult(
            success=True,
            access_token=None,
            user=user,
            requires_device_verification=True,
            verification_token="verify_token_123",
            device_info={"device_type": "mobile"},
            message="需要设备验证",
        )

        unified_result = unified_auth_adapter.from_sms_auth_result(sms_result)

        assert unified_result.requires_device_verification is True
        assert unified_result.verification_token == "verify_token_123"

        # 测试转换为设备验证响应
        device_response = unified_result.to_device_verification_response()
        assert isinstance(device_response, UnifiedDeviceVerificationResponse)
        assert device_response.requires_device_verification is True
        assert device_response.verification_token == "verify_token_123"
        assert device_response.auth_method == "sms"

    def test_from_wechat_bind_response(self):
        """测试从微信绑定响应转换"""
        wechat_user_info = WeChatUserInfo(
            id=1,
            username="13800138000",
            nickname="测试用户",
            wechat_nickname="微信昵称",
            avatar="https://wx.qlogo.cn/avatar.jpg",
        )

        wechat_response = WeChatBindResponse(
            message="绑定成功",
            user=wechat_user_info,
            access_token="wechat_token",
            token_type="bearer",
        )

        unified_result = unified_auth_adapter.from_wechat_bind_response(wechat_response)

        assert isinstance(unified_result, UnifiedAuthResult)
        assert unified_result.access_token == "wechat_token"
        assert unified_result.auth_method == "wechat"
        assert unified_result.message == "绑定成功"
        assert unified_result.user["wechat_nickname"] == "微信昵称"
        
        # 测试转换为认证响应
        auth_response = unified_result.to_auth_response()
        assert isinstance(auth_response, UnifiedAuthResponse)
        assert auth_response.access_token == "wechat_token"
        assert auth_response.auth_method == "wechat"

    def test_to_legacy_sms_response(self):
        """测试转换为传统SMS响应"""
        user_data = {
            "id": 1,
            "username": "13800138000",
            "nickname": "测试用户",
            "email": "<EMAIL>",
            "avatar": "https://example.com/avatar.jpg",
            "is_active": True,
            "last_login": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "wechat_nickname": None,
            "wechat_avatar": None,
            "login_type": "sms",
        }

        unified_response = UnifiedAuthResponse(
            access_token="test_token",
            token_type="bearer",
            user=UnifiedUserInfo.model_validate(user_data),
            message="登录成功",
            auth_method="sms",
        )

        legacy_response = unified_auth_adapter.to_legacy_sms_response(unified_response)

        assert isinstance(legacy_response, AuthResponse)
        assert legacy_response.access_token == "test_token"
        assert legacy_response.user.username == "13800138000"

    def test_to_legacy_wechat_response(self):
        """测试转换为传统微信响应"""
        user_data = {
            "id": 1,
            "username": "13800138000",
            "nickname": "测试用户",
            "email": None,
            "avatar": "https://wx.qlogo.cn/avatar.jpg",
            "is_active": True,
            "last_login": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "wechat_nickname": "微信昵称",
            "wechat_avatar": "https://wx.qlogo.cn/avatar.jpg",
            "login_type": "wechat",
        }

        unified_response = UnifiedAuthResponse(
            access_token="wechat_token",
            token_type="bearer",
            user=UnifiedUserInfo.model_validate(user_data),
            message="绑定成功",
            auth_method="wechat",
        )

        legacy_response = unified_auth_adapter.to_legacy_wechat_response(unified_response)

        assert isinstance(legacy_response, WeChatBindResponse)
        assert legacy_response.access_token == "wechat_token"
        assert legacy_response.user.wechat_nickname == "微信昵称"

    def test_create_unified_result(self):
        """测试创建统一认证结果"""
        user = self.create_mock_user()

        result = unified_auth_adapter.create_unified_result(
            success=True,
            access_token="test_token",
            user=user,
            requires_device_verification=False,
            message="登录成功",
            auth_method="sms",
        )

        assert isinstance(result, UnifiedAuthResult)
        assert result.success is True
        assert result.access_token == "test_token"
        assert result.auth_method == "sms"
        assert result.message == "登录成功"


class TestUnifiedAuthResult:
    """统一认证结果测试"""

    def test_auth_result_to_auth_response(self):
        """测试认证结果转换为认证响应"""
        user_data = {
            "id": 1,
            "username": "13800138000",
            "nickname": "测试用户",
            "email": None,
            "avatar": None,
            "is_active": True,
            "last_login": datetime.utcnow(),
            "created_at": datetime.utcnow(),
            "wechat_nickname": None,
            "wechat_avatar": None,
            "login_type": "sms",
        }

        result = UnifiedAuthResult(
            success=True, access_token="test_token", user=user_data, auth_method="sms"
        )

        response = result.to_auth_response()

        assert isinstance(response, UnifiedAuthResponse)
        assert response.access_token == "test_token"
        assert response.auth_method == "sms"
        assert response.user.username == "13800138000"

    def test_auth_result_to_device_verification_response(self):
        """测试认证结果转换为设备验证响应"""
        result = UnifiedAuthResult(
            success=False,
            requires_device_verification=True,
            verification_token="verify_token_123",
            device_info={"device_type": "mobile"},
            message="需要设备验证",
            auth_method="sms",
        )

        response = result.to_device_verification_response()

        assert isinstance(response, UnifiedDeviceVerificationResponse)
        assert response.requires_device_verification is True
        assert response.verification_token == "verify_token_123"
        assert response.auth_method == "sms"

    def test_auth_result_invalid_conversion(self):
        """测试无效转换"""
        # 测试缺少必要信息的认证结果
        result = UnifiedAuthResult(success=False, auth_method="sms")

        # 应该抛出异常
        with pytest.raises(ValueError, match="认证失败，无法生成响应"):
            result.to_auth_response()

        # 测试不需要设备验证时转换为设备验证响应
        result2 = UnifiedAuthResult(
            success=True, requires_device_verification=False, auth_method="sms"
        )

        with pytest.raises(ValueError, match="不需要设备验证，无法生成设备验证响应"):
            result2.to_device_verification_response()


if __name__ == "__main__":
    pytest.main([__file__])
