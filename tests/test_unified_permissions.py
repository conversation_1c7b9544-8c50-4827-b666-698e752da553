#!/usr/bin/env python3
"""
统一权限系统测试脚本

用于验证新的统一权限系统是否正常工作
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta

from app.core.permission_system import (
    ROLE_PERMISSIONS,
    Action,
    Permission,
    PermissionChecker,
    Permissions,
    ResourceType,
    Scope,
    get_permission_from_string,
    permission_cache,
)


class MockUser:
    """模拟用户对象"""
    def __init__(self, user_id: int, is_superuser: bool = False, role_name: str = "普通用户"):
        self.id = user_id
        self.is_superuser = is_superuser
        self.role = MockRole(role_name)
        self.permissions = []  # 兼容旧系统


class MockRole:
    """模拟角色对象"""
    def __init__(self, name: str):
        self.name = name


class MockArticle:
    """模拟文章对象"""
    def __init__(self, article_id: int, author_id: int, is_published: bool = True, is_approved: bool = True):
        self.id = article_id
        self.author_id = author_id
        self.is_published = is_published
        self.is_approved = is_approved


def test_permission_creation():
    """测试权限对象创建"""
    print("\n=== 测试权限对象创建 ===")
    
    # 基本权限
    perm1 = Permission(ResourceType.ARTICLE, Action.READ, Scope.PUBLIC)
    print(f"基本权限: {perm1}")
    
    # 带条件的权限
    perm2 = Permission(
        ResourceType.ARTICLE, 
        Action.UPDATE, 
        Scope.OWN,
        conditions={"status": "draft"},
        description="只能编辑草稿文章"
    )
    print(f"条件权限: {perm2}")
    
    # 带过期时间的权限
    perm3 = Permission(
        ResourceType.VIDEO,
        Action.UPLOAD,
        Scope.ALL,
        expires_at=datetime.now() + timedelta(hours=1)
    )
    print(f"临时权限: {perm3}")
    print(f"权限是否过期: {perm3.is_expired()}")
    
    return True


def test_permission_from_string():
    """测试从字符串创建权限"""
    print("\n=== 测试字符串权限解析 ===")
    
    # 测试新格式
    try:
        perm1 = Permission.from_string("ARTICLE:READ:PUBLIC")
        print(f"新格式解析: {perm1}")
    except Exception as e:
        print(f"新格式解析失败: {e}")
    
    # 测试旧格式映射
    legacy_permissions = [
        "users:list",
        "articles:create",
        "videos:update",
        "roles:manage"
    ]
    
    for legacy_perm in legacy_permissions:
        try:
            perm = get_permission_from_string(legacy_perm)
            print(f"旧格式 '{legacy_perm}' -> {perm}")
        except Exception as e:
            print(f"旧格式解析失败 '{legacy_perm}': {e}")
    
    return True


def test_role_permissions():
    """测试角色权限映射"""
    print("\n=== 测试角色权限映射 ===")
    
    for role, permissions in ROLE_PERMISSIONS.items():
        print(f"\n{role.value} 角色权限:")
        for perm in list(permissions)[:5]:  # 只显示前5个权限
            print(f"  - {perm}")
        if len(permissions) > 5:
            print(f"  ... 还有 {len(permissions) - 5} 个权限")
    
    return True


def test_permission_checker():
    """测试权限检查器"""
    print("\n=== 测试权限检查器 ===")
    
    # 创建测试用户
    guest = None
    user = MockUser(1, role_name="普通用户")
    admin = MockUser(2, role_name="管理员")
    superuser = MockUser(3, is_superuser=True)
    
    # 创建测试文章
    public_article = MockArticle(1, author_id=1, is_published=True, is_approved=True)
    draft_article = MockArticle(2, author_id=1, is_published=False, is_approved=False)
    
    # 测试用例
    test_cases = [
        (guest, Permissions.ARTICLE_READ_PUBLIC, public_article, "游客读取公开文章"),
        (guest, Permissions.ARTICLE_READ_PUBLIC, draft_article, "游客读取草稿文章"),
        (user, Permissions.ARTICLE_CREATE, None, "普通用户创建文章"),
        (user, Permissions.USER_MANAGE, None, "普通用户管理用户"),
        (admin, Permissions.ARTICLE_MANAGE, None, "管理员管理文章"),
        (admin, Permissions.USER_CREATE, None, "管理员创建用户"),
        (superuser, Permissions.SYSTEM_MANAGE, None, "超级用户管理系统"),
    ]
    
    for test_user, permission, resource, description in test_cases:
        try:
            result = PermissionChecker.check_permission(test_user, permission, resource)
            status = "✓" if result else "✗"
            user_desc = "游客" if test_user is None else f"用户{test_user.id}"
            print(f"{status} {description}: {user_desc} -> {result}")
        except Exception as e:
            print(f"✗ {description}: 错误 - {e}")
    
    return True


def test_permission_cache():
    """测试权限缓存"""
    print("\n=== 测试权限缓存 ===")
    
    user = MockUser(1, role_name="普通用户")
    permission = Permissions.ARTICLE_CREATE
    
    # 清除缓存
    permission_cache.clear_all()
    print("缓存已清除")
    
    # 第一次检查（应该计算并缓存）
    result1 = PermissionChecker.check_permission(user, permission)
    print(f"第一次检查结果: {result1}")
    
    # 第二次检查（应该从缓存获取）
    result2 = PermissionChecker.check_permission(user, permission)
    print(f"第二次检查结果: {result2}")
    
    # 检查缓存
    cached_result = permission_cache.get(user.id, permission)
    print(f"缓存中的结果: {cached_result}")
    
    # 清除用户缓存
    permission_cache.clear_user_cache(user.id)
    cached_result_after_clear = permission_cache.get(user.id, permission)
    print(f"清除后缓存结果: {cached_result_after_clear}")
    
    return True


def test_legacy_compatibility():
    """测试旧系统兼容性"""
    print("\n=== 测试旧系统兼容性 ===")
    
    # 模拟旧系统的权限检查
    user = MockUser(1, role_name="管理员")
    user.permissions = ["users:list", "articles:create", "videos:update"]
    
    # 测试旧格式权限
    legacy_permissions = ["users:list", "articles:create", "videos:delete"]
    
    for legacy_perm in legacy_permissions:
        # 使用新系统检查
        try:
            permission = get_permission_from_string(legacy_perm)
            new_result = PermissionChecker.check_permission(user, permission)
            
            # 模拟旧系统检查
            old_result = legacy_perm in user.permissions
            
            status = "✓" if new_result or old_result else "✗"
            print(f"{status} {legacy_perm}: 新系统={new_result}, 旧系统={old_result}")
        except Exception as e:
            print(f"✗ {legacy_perm}: 错误 - {e}")
    
    return True


def main():
    """运行所有测试"""
    print("统一权限系统测试开始...")
    
    tests = [
        test_permission_creation,
        test_permission_from_string,
        test_role_permissions,
        test_permission_checker,
        test_permission_cache,
        test_legacy_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_func.__name__} 通过")
            else:
                print(f"✗ {test_func.__name__} 失败")
        except Exception as e:
            print(f"✗ {test_func.__name__} 异常: {e}")
    
    print("\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！统一权限系统工作正常。")
        return 0
    else:
        print(f"\n❌ {total-passed} 个测试失败，请检查权限系统配置。")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)