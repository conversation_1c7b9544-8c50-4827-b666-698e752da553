#!/usr/bin/env python3
"""
微信认证服务使用示例

本文件展示了如何使用微信认证模块的各种功能，包括：
1. 二维码登录
2. 微信账号绑定
3. 获取微信信息
4. 解除微信绑定
5. 错误处理
"""

import asyncio
from typing import Any

import httpx


class WeChatAuthClient:
    """微信认证客户端示例"""

    def __init__(self, base_url: str = "http://localhost:8000", token: str | None = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"

    async def create_qr_code(self) -> dict[str, Any]:
        """创建登录二维码"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/wechat/qr-login/create", headers=self.headers
            )

            if response.status_code == 200:
                data = response.json()
                print("✅ 二维码创建成功")
                print(f"场景字符串: {data['scene_str']}")
                print(f"二维码URL: {data['qr_url']}")
                print(f"过期时间: {data['expire_seconds']}秒")
                return data
            else:
                print(f"❌ 二维码创建失败: {response.text}")
                return {}

    async def check_qr_status(self, scene_str: str) -> dict[str, Any]:
        """检查二维码登录状态"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/wechat/qr-login/status/{scene_str}", headers=self.headers
            )

            if response.status_code == 200:
                data = response.json()
                status = data.get("status", "unknown")
                print(f"二维码状态: {status}")

                if status == "success" and "access_token" in data:
                    print("✅ 登录成功！")
                    print(f"用户: {data.get('user', {}).get('username', 'Unknown')}")
                    # 更新token
                    self.token = data["access_token"]
                    self.headers["Authorization"] = f"Bearer {self.token}"

                return data
            else:
                print(f"❌ 状态检查失败: {response.text}")
                return {}

    async def bind_wechat(self, openid: str, username: str, code: str) -> dict[str, Any]:
        """绑定微信账号"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/wechat/bind",
                headers=self.headers,
                json={"openid": openid, "username": username, "code": code},
            )

            if response.status_code == 200:
                data = response.json()
                print(f"✅ {data.get('message', '绑定成功')}")
                print(f"用户: {data.get('user', {}).get('username', 'Unknown')}")

                # 更新token
                if "access_token" in data:
                    self.token = data["access_token"]
                    self.headers["Authorization"] = f"Bearer {self.token}"

                return data
            else:
                error_data = (
                    response.json()
                    if response.headers.get("content-type", "").startswith("application/json")
                    else {}
                )
                print(f"❌ 绑定失败: {error_data.get('detail', response.text)}")
                return {}

    async def get_wechat_info(self) -> dict[str, Any]:
        """获取微信绑定信息"""
        if not self.token:
            print("❌ 需要先登录")
            return {}

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/api/v1/wechat/info", headers=self.headers)

            if response.status_code == 200:
                data = response.json()
                print(f"微信绑定状态: {'已绑定' if data.get('is_bound') else '未绑定'}")

                if data.get("is_bound"):
                    print(f"微信昵称: {data.get('wechat_nickname', 'Unknown')}")
                    print(f"微信头像: {data.get('wechat_avatar', 'None')}")

                return data
            else:
                print(f"❌ 获取信息失败: {response.text}")
                return {}

    async def unbind_wechat(self) -> dict[str, Any]:
        """解除微信绑定"""
        if not self.token:
            print("❌ 需要先登录")
            return {}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/wechat/unbind", headers=self.headers
            )

            if response.status_code == 200:
                data = response.json()
                print(f"✅ {data.get('message', '解绑成功')}")
                return data
            else:
                error_data = (
                    response.json()
                    if response.headers.get("content-type", "").startswith("application/json")
                    else {}
                )
                print(f"❌ 解绑失败: {error_data.get('detail', response.text)}")
                return {}


async def demo_qr_login():
    """演示二维码登录流程"""
    print("\n=== 二维码登录演示 ===")

    client = WeChatAuthClient()

    # 1. 创建二维码
    qr_data = await client.create_qr_code()
    if not qr_data:
        return

    scene_str = qr_data["scene_str"]
    print(f"\n请使用微信扫描二维码: {qr_data['qr_url']}")
    print("等待用户扫码...")

    # 2. 轮询检查状态
    max_attempts = 30  # 最多检查30次
    for i in range(max_attempts):
        await asyncio.sleep(2)  # 每2秒检查一次

        status_data = await client.check_qr_status(scene_str)
        status = status_data.get("status", "unknown")

        if status == "success":
            print("🎉 登录成功！")
            return client  # 返回已登录的客户端
        elif status == "expired":
            print("⏰ 二维码已过期")
            break
        elif status == "cancelled":
            print("❌ 用户取消登录")
            break

        print(f"等待中... ({i + 1}/{max_attempts})")

    print("登录超时或失败")
    return None


async def demo_bind_flow():
    """演示绑定流程"""
    print("\n=== 微信绑定演示 ===")

    client = WeChatAuthClient()

    # 模拟绑定参数
    test_data = {
        "openid": "demo_openid_123456",
        "username": "13800138000",
        "code": "123456",  # 实际使用时需要真实的验证码
    }

    print("尝试绑定微信账号...")
    print(f"OpenID: {test_data['openid']}")
    print(f"手机号: {test_data['username']}")

    result = await client.bind_wechat(**test_data)

    if result:
        print("绑定成功，现在可以使用微信相关功能")
        return client
    else:
        print("绑定失败")
        return None


async def demo_wechat_management(client: WeChatAuthClient):
    """演示微信账号管理"""
    print("\n=== 微信账号管理演示 ===")

    # 1. 获取微信信息
    print("\n1. 获取微信绑定信息:")
    await client.get_wechat_info()

    # 2. 解除绑定（可选）
    print("\n2. 是否要解除微信绑定？(y/n)")
    # 在实际应用中，这里可以接收用户输入
    # choice = input().lower()
    choice = "n"  # 演示中默认不解绑

    if choice == "y":
        print("解除微信绑定...")
        await client.unbind_wechat()

        # 再次检查状态
        print("\n解绑后的状态:")
        await client.get_wechat_info()
    else:
        print("保持微信绑定状态")


async def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===")

    client = WeChatAuthClient()

    # 1. 测试无效的绑定请求
    print("\n1. 测试无效验证码:")
    await client.bind_wechat(
        openid="invalid_openid",
        username="13800138000",
        code="000000",  # 无效验证码
    )

    # 2. 测试未登录状态下的操作
    print("\n2. 测试未登录状态下获取信息:")
    await client.get_wechat_info()

    # 3. 测试重复绑定
    print("\n3. 测试重复绑定:")
    await client.bind_wechat(openid="existing_openid", username="13900139000", code="123456")


async def main():
    """主演示函数"""
    print("🚀 微信认证服务演示程序")
    print("=" * 50)

    try:
        # 演示1: 二维码登录
        logged_client = await demo_qr_login()

        if logged_client:
            # 如果二维码登录成功，演示账号管理
            await demo_wechat_management(logged_client)
        else:
            # 如果二维码登录失败，演示绑定流程
            bound_client = await demo_bind_flow()

            if bound_client:
                await demo_wechat_management(bound_client)

        # 演示错误处理
        await demo_error_handling()

        print("\n🎉 演示完成！")

    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
