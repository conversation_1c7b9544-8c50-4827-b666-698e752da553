#!/usr/bin/env python3
"""
统一登录注册功能使用示例
"""

import asyncio

import httpx


class UnifiedAuthClient:
    """统一认证客户端示例"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token = None

    async def send_auth_code(self, phone: str) -> dict:
        """发送验证码（统一接口）"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/sms-auth/send-auth-code", json={"phone": phone}
            )
            response.raise_for_status()
            return response.json()

    async def verify_auth_code(self, phone: str, code: str) -> dict:
        """验证验证码（统一接口）"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/v1/sms-auth/verify-auth-code",
                json={"phone": phone, "code": code},
            )
            response.raise_for_status()
            return response.json()

    async def authenticate(self, phone: str, code: str) -> bool:
        """完整的认证流程"""
        try:
            # 验证验证码
            result = await self.verify_auth_code(phone, code)

            if "access_token" in result:
                # 直接登录成功
                self.access_token = result["access_token"]
                user = result.get("user", {})
                print("✅ 认证成功！")
                print(f"用户: {user.get('nickname')} ({user.get('username')})")
                print(f"登录类型: {user.get('login_type')}")
                return True

            elif result.get("requires_device_verification"):
                # 需要设备验证
                print("🔐 需要设备验证")
                print(f"消息: {result.get('message')}")
                device_info = result.get("device_info", {})
                print(f"设备: {device_info.get('device_name')} ({device_info.get('device_type')})")
                print(f"位置: {device_info.get('location')}")

                # 这里可以继续处理设备验证流程
                verification_token = result.get("verification_token")
                print(f"设备验证令牌: {verification_token[:50]}...")
                return False

        except httpx.HTTPStatusError as e:
            print(f"❌ 认证失败: {e.response.text}")
            return False
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            return False

    async def get_user_info(self) -> dict:
        """获取用户信息（需要先认证）"""
        if not self.access_token:
            raise ValueError("请先进行认证")

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/api/v1/users/me",
                headers={"Authorization": f"Bearer {self.access_token}"},
            )
            response.raise_for_status()
            return response.json()


async def demo_new_user():
    """演示新用户自动注册流程"""
    print("=== 新用户自动注册演示 ===")

    client = UnifiedAuthClient()
    phone = "13900139001"  # 使用一个新的手机号

    # 1. 发送验证码
    print(f"1. 为手机号 {phone} 发送验证码...")
    try:
        result = await client.send_auth_code(phone)
        print(f"发送结果: {result}")

        if not result.get("is_registered"):
            print("✨ 检测到新用户，将自动注册")
        else:
            print("👤 用户已存在，将直接登录")

        # 2. 模拟用户输入验证码
        print("\n2. 请查看服务器日志获取验证码...")
        code = input("请输入验证码: ")

        # 3. 验证并认证
        print("\n3. 验证验证码...")
        success = await client.authenticate(phone, code)

        if success:
            # 4. 获取用户信息
            print("\n4. 获取用户信息...")
            user_info = await client.get_user_info()
            print(f"用户详细信息: {user_info}")

    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")


async def demo_existing_user():
    """演示已有用户登录流程"""
    print("\n=== 已有用户登录演示 ===")

    client = UnifiedAuthClient()
    phone = "13800138000"  # 使用一个已存在的手机号

    # 1. 发送验证码
    print(f"1. 为手机号 {phone} 发送验证码...")
    try:
        result = await client.send_auth_code(phone)
        print(f"发送结果: {result}")

        if result.get("is_registered"):
            print("👤 用户已存在，将直接登录")
        else:
            print("✨ 检测到新用户，将自动注册")

        # 2. 模拟用户输入验证码
        print("\n2. 请查看服务器日志获取验证码...")
        code = input("请输入验证码: ")

        # 3. 验证并认证
        print("\n3. 验证验证码...")
        success = await client.authenticate(phone, code)

        if success:
            # 4. 获取用户信息
            print("\n4. 获取用户信息...")
            user_info = await client.get_user_info()
            print(f"用户详细信息: {user_info}")

    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")


async def main():
    """主演示函数"""
    print("统一登录注册功能演示")
    print("=" * 50)
    print("请确保服务器正在运行在 http://localhost:8000")
    print("=" * 50)

    # 演示新用户自动注册
    await demo_new_user()

    # 演示已有用户登录
    await demo_existing_user()

    print("\n演示完成！")


if __name__ == "__main__":
    asyncio.run(main())
