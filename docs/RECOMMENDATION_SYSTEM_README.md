# 推荐系统实现文档

## 功能概述

本项目实现了一个完整的文章和视频推荐系统，支持多种推荐算法、用户行为追踪、缓存优化和效果评估。

## 主要特性

### ✅ 已实现功能

1. **多种推荐算法**
   - 协同过滤推荐（基于用户相似度）
   - 基于内容的推荐（标签、分类匹配）
   - 热门内容推荐（基于互动热度）
   - 混合推荐算法（综合多种策略）

2. **用户行为追踪**
   - 浏览历史记录（时长、来源、设备类型）
   - 交互行为追踪（点赞、收藏、评论、分享等）
   - 用户画像构建（兴趣标签、偏好分类）
   - 自动画像更新

3. **缓存优化**
   - Redis缓存推荐结果
   - 用户画像缓存
   - 热门内容缓存
   - 相似内容缓存
   - 智能缓存失效

4. **效果评估**
   - 点击率（CTR）统计
   - 精确率和召回率计算
   - 推荐多样性评估
   - 覆盖率分析
   - 新颖性评估
   - 综合评估报告

## 系统架构

### 数据模型

#### 用户行为相关
- `UserBrowseHistory`: 用户浏览历史
- `UserInteraction`: 用户交互行为
- `UserProfile`: 用户画像
- `RecommendationLog`: 推荐记录
- `ContentSimilarity`: 内容相似度

### 服务层

#### 核心服务
- `RecommendationService`: 推荐算法核心服务
- `RecommendationCacheService`: 推荐缓存服务
- `BehaviorTrackingService`: 用户行为追踪服务
- `RecommendationEvaluationService`: 推荐效果评估服务

### API接口

#### 推荐相关 API (`/api/v1/recommendations`)
- `GET /`: 获取个性化推荐
- `GET /similar/{content_type}/{content_id}`: 获取相似内容
- `GET /hot/{content_type}`: 获取热门内容
- `POST /feedback`: 提交推荐反馈
- `GET /stats`: 获取推荐统计
- `GET /evaluation/report`: 获取评估报告
- `GET /evaluation/algorithm/{algorithm_type}`: 获取算法评估
- `GET /evaluation/user/{user_id}`: 获取用户评估

#### 用户行为 API (`/api/v1/user/behavior`)
- `POST /browse`: 记录浏览历史
- `POST /interaction`: 记录交互行为
- `GET /browse-history`: 获取浏览历史
- `GET /interactions`: 获取交互记录
- `GET /profile`: 获取用户画像
- `PUT /profile`: 更新用户画像
- `GET /interest-analysis`: 获取兴趣分析
- `GET /popular-content`: 获取热门浏览内容

## 推荐算法详解

### 1. 协同过滤推荐
- **原理**: 基于用户相似度，推荐相似用户喜欢的内容
- **实现**: 使用Jaccard相似度计算用户相似性
- **优势**: 能发现用户潜在兴趣
- **适用场景**: 用户数据充足的情况

### 2. 基于内容的推荐
- **原理**: 基于用户历史偏好，推荐相似内容
- **实现**: 分析用户画像中的标签和分类偏好
- **优势**: 推荐结果可解释性强
- **适用场景**: 新用户或内容丰富的场景

### 3. 热门推荐
- **原理**: 基于内容的热度分数推荐
- **计算公式**: `热度 = 点赞数×3 + 收藏数×5 + 评论数×4`
- **优势**: 保证推荐质量的下限
- **适用场景**: 冷启动问题的解决方案

### 4. 混合推荐
- **原理**: 综合多种算法的结果
- **权重分配**: 协同过滤40% + 内容推荐30% + 热门推荐30%
- **优势**: 平衡各种算法的优缺点
- **适用场景**: 生产环境的主要推荐策略

## 用户画像构建

### 兴趣标签
- 基于用户交互的内容标签自动提取
- 权重随交互强度动态调整
- 支持标签权重归一化

### 偏好分类
- 基于用户浏览和交互的内容分类统计
- 动态更新分类偏好权重
- 支持多级分类体系

### 行为模式
- 设备使用偏好（移动端/桌面端）
- 活跃时间段分析
- 内容类型偏好（文章/视频）

## 缓存策略

### 缓存层级
1. **用户推荐缓存**: 30分钟
2. **热门内容缓存**: 1小时
3. **相似内容缓存**: 2小时
4. **用户画像缓存**: 24小时

### 缓存失效策略
- 用户行为变化时自动失效相关缓存
- 内容更新时失效相关推荐缓存
- 支持手动缓存清理

## 效果评估指标

### 1. 点击率 (CTR)
```
CTR = 点击数 / 推荐数
```

### 2. 精确率和召回率
```
精确率 = 相关且被推荐的内容数 / 被推荐的内容数
召回率 = 相关且被推荐的内容数 / 相关的内容数
F1分数 = 2 × (精确率 × 召回率) / (精确率 + 召回率)
```

### 3. 多样性
```
多样性 = (分类多样性 + 内容类型多样性) / 2
```

### 4. 覆盖率
```
覆盖率 = 被推荐的唯一内容数 / 总可推荐内容数
```

### 5. 新颖性
```
新颖性 = 用户未接触过的推荐内容数 / 总推荐内容数
```

## 部署和配置

### 1. 依赖安装
```bash
pip install -r requirements.txt
```

### 2. 数据库迁移
```bash
# 创建推荐相关表
python migrate_database.py
```

### 3. Redis 配置
确保Redis服务正常运行，配置连接信息：
```python
REDIS_URL = "redis://localhost:6379/0"
```

### 4. 环境变量
```bash
# 推荐系统配置
RECOMMENDATION_CACHE_EXPIRE=1800
HOT_CONTENT_CACHE_EXPIRE=3600
USER_PROFILE_CACHE_EXPIRE=86400
```

## API 使用示例

### 获取个性化推荐
```http
GET /api/v1/recommendations/?algorithm_type=hybrid&limit=10&content_type=article
Authorization: Bearer <token>
```

### 记录用户浏览行为
```http
POST /api/v1/user/behavior/browse
Content-Type: application/json
Authorization: Bearer <token>

{
    "content_type": "article",
    "content_id": 123,
    "duration": 120,
    "source": "homepage"
}
```

### 获取推荐效果评估
```http
GET /api/v1/recommendations/evaluation/report?days=7
Authorization: Bearer <admin_token>
```

## 性能优化建议

### 1. 数据库优化
- 为用户行为表添加适当索引
- 定期清理过期的行为数据
- 使用数据库分区优化大表查询

### 2. 缓存优化
- 根据业务需求调整缓存过期时间
- 使用Redis集群提高缓存可用性
- 实现缓存预热机制

### 3. 算法优化
- 定期重新计算内容相似度
- 使用异步任务更新用户画像
- 实现增量更新机制

## 监控和维护

### 1. 关键指标监控
- 推荐系统整体CTR
- 各算法性能对比
- 缓存命中率
- API响应时间

### 2. 数据质量监控
- 用户行为数据完整性
- 推荐结果多样性
- 系统覆盖率变化

### 3. 定期维护任务
- 清理过期的推荐日志
- 更新内容相似度矩阵
- 优化用户画像数据
- 评估和调整算法参数

## 扩展功能建议

### 1. 高级算法
- 深度学习推荐模型
- 实时推荐系统
- 多臂老虎机算法
- 强化学习推荐

### 2. 个性化增强
- 时间感知推荐
- 地理位置推荐
- 社交网络推荐
- 情感分析推荐

### 3. 业务功能
- A/B测试框架
- 推荐解释系统
- 用户反馈学习
- 冷启动优化

## 故障排除

### 常见问题
1. **推荐结果为空**: 检查用户画像和内容数据
2. **缓存失效**: 检查Redis连接和配置
3. **性能问题**: 检查数据库索引和查询优化
4. **算法效果差**: 检查用户行为数据质量

### 日志分析
- 查看推荐服务日志了解算法执行情况
- 监控用户行为追踪日志确保数据完整性
- 分析评估报告识别系统瓶颈
