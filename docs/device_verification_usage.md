# 设备验证功能使用说明

## 功能概述

设备验证功能可以在用户使用不常用设备登录时要求额外的短信验证，提高账户安全性。

## 工作流程

### 1. 正常登录流程

```
用户输入手机号 → 发送登录验证码 → 验证验证码 → 检查设备信任度
```

- 如果是信任设备：直接登录成功
- 如果是不信任设备：要求设备验证

### 2. 设备验证流程

```
检测到不信任设备 → 返回设备验证响应 → 发送设备验证码 → 验证设备验证码 → 标记设备为信任 → 登录成功
```

## API 接口

### 1. 发送登录验证码
```http
POST /api/v1/sms-auth/send-login-code
Content-Type: application/json

{
    "phone": "13800138000"
}
```

### 2. 验证登录验证码
```http
POST /api/v1/sms-auth/verify-login-code
Content-Type: application/json

{
    "phone": "13800138000",
    "code": "123456"
}
```

**响应情况1：信任设备，直接登录**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "user": {
        "id": 1,
        "username": "13800138000",
        "phone": "13800138000",
        "nickname": "用户昵称",
        "is_active": true,
        "created_at": "2024-01-01T00:00:00"
    }
}
```

**响应情况2：不信任设备，需要设备验证**
```json
{
    "requires_device_verification": true,
    "message": "检测到新设备登录，需要进行设备验证",
    "device_info": {
        "device_name": "iPhone - iOS",
        "device_type": "mobile",
        "location": "未知位置",
        "is_new_device": true
    },
    "verification_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 3. 发送设备验证码
```http
POST /api/v1/sms-auth/send-device-verification-code
Content-Type: application/json

{
    "verification_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 4. 验证设备验证码
```http
POST /api/v1/sms-auth/verify-device-code
Content-Type: application/json

{
    "phone": "13800138000",
    "code": "654321",
    "verification_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## 设备管理接口

### 1. 获取设备列表
```http
GET /api/v1/device-management/devices
Authorization: Bearer <access_token>
```

### 2. 信任设备
```http
POST /api/v1/device-management/devices/trust
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "device_id": 1,
    "reason": "这是我的常用设备"
}
```

### 3. 阻止设备
```http
POST /api/v1/device-management/devices/block
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "device_id": 2,
    "reason": "可疑设备"
}
```

## 设备信任度评估规则

### 信任分数计算（满分100分）

1. **登录次数**（最多40分）：每次登录+2分
2. **使用时长**（最多30分）：每天使用+1分
3. **最近活跃度**（最多20分）：
   - 7天内登录：+20分
   - 30天内登录：+10分
4. **手动信任**（最多10分）：用户手动标记为信任

### 设备验证触发条件

以下情况需要设备验证：

1. **新设备**：首次登录不超过24小时
2. **低信任分数**：信任分数低于40分
3. **长期未使用**：超过90天未登录
4. **被手动标记为不信任**

### 免验证条件

以下情况可以免设备验证：

1. **手动信任设备**：用户手动标记为信任
2. **高信任分数**：信任分数≥60分
3. **常用设备**：登录次数≥5次且30天内有登录，且信任分数≥40分

## 前端集成示例

```javascript
// 登录函数
async function login(phone, code) {
    const response = await fetch('/api/v1/sms-auth/verify-login-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone, code })
    });
    
    const data = await response.json();
    
    if (data.requires_device_verification) {
        // 需要设备验证
        showDeviceVerificationDialog(data);
    } else {
        // 直接登录成功
        handleLoginSuccess(data);
    }
}

// 设备验证函数
async function verifyDevice(phone, code, verificationToken) {
    const response = await fetch('/api/v1/sms-auth/verify-device-code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            phone, 
            code, 
            verification_token: verificationToken 
        })
    });
    
    const data = await response.json();
    handleLoginSuccess(data);
}
```

## 安全建议

1. **设备验证码有效期**：建议设置为5-10分钟
2. **验证令牌有效期**：建议设置为10分钟
3. **信任设备定期清理**：建议定期清理长期未使用的信任设备
4. **异常登录通知**：建议在检测到异常登录时发送通知
5. **IP地址监控**：可以结合IP地址变化进行风险评估
