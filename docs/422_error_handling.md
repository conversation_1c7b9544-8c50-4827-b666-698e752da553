# FastAPI 422错误处理完整指南

## 1. 422错误概述

422 (Unprocessable Entity) 错误在FastAPI中主要发生在以下情况：

- **请求数据验证失败**：Pydantic模型验证不通过
- **类型错误**：字段类型与期望不符
- **值错误**：字段值不满足验证规则
- **缺少必需字段**：请求中缺少必需的字段
- **格式错误**：数据格式不正确（如邮箱格式）

## 2. 自定义422错误处理器

在 `app/main.py` 中实现了全局422错误处理器：

```python
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误 (422)"""
    errors = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        error_msg = error["msg"]
        error_type = error["type"]
        
        # 自定义错误消息
        if error_type == "missing":
            custom_msg = f"缺少必需字段: {field_path}"
        elif error_type == "type_error":
            custom_msg = f"字段类型错误: {field_path} - {error_msg}"
        elif error_type == "value_error":
            custom_msg = f"字段值错误: {field_path} - {error_msg}"
        else:
            custom_msg = f"{field_path}: {error_msg}"
        
        errors.append({
            "field": field_path,
            "message": custom_msg,
            "type": error_type,
            "input": error.get("input")
        })
    
    return JSONResponse(
        status_code=422,
        content={
            "status": "error",
            "message": "请求数据验证失败",
            "errors": errors,
            "detail": "请检查请求参数格式和类型"
        }
    )
```

## 3. 错误响应格式

统一的422错误响应格式：

```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "age",
            "message": "字段值错误: age - ensure this value is greater than or equal to 18",
            "type": "value_error.number.not_ge",
            "input": 15
        }
    ],
    "detail": "请检查请求参数格式和类型"
}
```

## 4. 常见422错误场景

### 4.1 缺少必需字段

**请求示例：**
```bash
POST /api/v1/validation/users/demo
{
    "username": "test"
    # 缺少email和age字段
}
```

**错误响应：**
```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "email",
            "message": "缺少必需字段: email",
            "type": "missing"
        },
        {
            "field": "age",
            "message": "缺少必需字段: age",
            "type": "missing"
        }
    ]
}
```

### 4.2 字段类型错误

**请求示例：**
```bash
POST /api/v1/validation/users/demo
{
    "username": "test",
    "email": "<EMAIL>",
    "age": "not_a_number"
}
```

**错误响应：**
```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "age",
            "message": "字段类型错误: age - value is not a valid integer",
            "type": "type_error.integer",
            "input": "not_a_number"
        }
    ]
}
```

### 4.3 字段值验证失败

**请求示例：**
```bash
POST /api/v1/validation/users/demo
{
    "username": "test",
    "email": "<EMAIL>",
    "age": 15
}
```

**错误响应：**
```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "age",
            "message": "字段值错误: age - ensure this value is greater than or equal to 18",
            "type": "value_error.number.not_ge",
            "input": 15
        }
    ]
}
```

### 4.4 枚举值错误

**请求示例：**
```bash
POST /api/v1/validation/users/demo
{
    "username": "test",
    "email": "<EMAIL>",
    "age": 25,
    "role": "invalid_role"
}
```

**错误响应：**
```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "role",
            "message": "字段值错误: role - value is not a valid enumeration member",
            "type": "type_error.enum",
            "input": "invalid_role"
        }
    ]
}
```

### 4.5 自定义验证器失败

**请求示例：**
```bash
POST /api/v1/validation/users/demo
{
    "username": "test@#$",
    "email": "<EMAIL>",
    "age": 25
}
```

**错误响应：**
```json
{
    "status": "error",
    "message": "请求数据验证失败",
    "errors": [
        {
            "field": "username",
            "message": "字段值错误: username - 用户名只能包含字母和数字",
            "type": "value_error",
            "input": "test@#$"
        }
    ]
}
```

## 5. Pydantic模型验证最佳实践

### 5.1 使用Field进行字段验证

```python
from pydantic import BaseModel, Field, EmailStr, validator

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=20, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    age: int = Field(..., ge=18, le=120, description="年龄")
    password: str = Field(..., min_length=8, description="密码")
```

### 5.2 自定义验证器

```python
class UserCreate(BaseModel):
    username: str
    
    @validator('username')
    def validate_username(cls, v):
        if not v.isalnum():
            raise ValueError('用户名只能包含字母和数字')
        return v
```

### 5.3 枚举类型验证

```python
from enum import Enum

class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    GUEST = "guest"

class UserCreate(BaseModel):
    role: UserRole = Field(default=UserRole.USER)
```

## 6. 查询参数验证

```python
@router.get("/search")
async def search(
    q: str = Query(..., min_length=1, max_length=100),
    page: int = Query(1, ge=1, le=1000),
    size: int = Query(10, ge=1, le=100)
):
    pass
```

## 7. 手动抛出422错误

```python
from fastapi import HTTPException

@router.post("/custom-validation")
async def custom_validation(data: dict):
    if "required_field" not in data:
        raise HTTPException(
            status_code=422,
            detail="缺少必需字段: required_field"
        )
    return {"message": "验证通过"}
```

## 8. 测试422错误处理

```python
def test_validation_error():
    response = client.post(
        "/api/v1/validation/users/demo",
        json={"username": "test"}  # 缺少必需字段
    )
    
    assert response.status_code == 422
    data = response.json()
    assert data["status"] == "error"
    assert "errors" in data
```

## 9. 前端处理建议

### 9.1 JavaScript处理示例

```javascript
async function createUser(userData) {
    try {
        const response = await fetch('/api/v1/validation/users/demo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(userData)
        });
        
        if (response.status === 422) {
            const errorData = await response.json();
            
            // 显示字段级错误
            errorData.errors.forEach(error => {
                showFieldError(error.field, error.message);
            });
            
            return;
        }
        
        const data = await response.json();
        console.log('用户创建成功:', data);
        
    } catch (error) {
        console.error('请求失败:', error);
    }
}

function showFieldError(field, message) {
    const fieldElement = document.querySelector(`[name="${field}"]`);
    if (fieldElement) {
        // 显示错误消息
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        fieldElement.parentNode.appendChild(errorElement);
    }
}
```

## 10. 最佳实践总结

1. **统一错误格式**：使用全局异常处理器统一422错误响应格式
2. **详细错误信息**：提供具体的字段名和错误原因
3. **友好的错误消息**：将技术性错误转换为用户友好的消息
4. **字段级验证**：使用Pydantic的Field和validator进行细粒度验证
5. **前端友好**：错误响应格式便于前端解析和显示
6. **测试覆盖**：为各种验证场景编写测试用例
7. **文档完善**：在API文档中说明可能的验证错误

通过这些实践，可以为用户提供清晰、有用的错误信息，提升API的可用性和开发体验。
