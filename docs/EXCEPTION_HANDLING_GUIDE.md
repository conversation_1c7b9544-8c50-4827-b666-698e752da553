# 统一异常处理指南

## 问题背景

在之前的代码中，每个API接口都需要重复编写相同的异常处理逻辑：

```python
try:
    # 业务逻辑
    result = await some_service.do_something()
    return result
except ValueError as e:
    raise HTTPException(status_code=400, detail=str(e)) from e
except HTTPException:
    raise  # 让HTTPException直接向上传播
except Exception as e:
    logger.error(f"操作失败：{str(e)}")
    raise HTTPException(status_code=500, detail="内部服务器错误") from e
```

这种方式存在以下问题：
1. **代码重复**：每个接口都要写相同的异常处理代码
2. **维护困难**：修改异常处理逻辑需要改动多个文件
3. **容易遗漏**：新接口可能忘记添加异常处理
4. **不一致**：不同接口的异常处理可能不一致

## 解决方案：统一异常处理中间件

我们使用 `ExceptionHandlerMiddleware` 来统一处理所有异常，让API接口专注于业务逻辑。

### 中间件工作原理

```python
class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """统一异常处理中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        try:
            response = await call_next(request)
            return response
        except HTTPException as http_exc:
            # HTTPException 直接返回，保持原有的状态码和消息
            return JSONResponse(
                status_code=http_exc.status_code, 
                content={"detail": http_exc.detail}
            )
        except Exception as e:
            # 记录真正的服务器错误
            logger.error(f"未处理的异常: {str(e)} - {request.method} {request.url.path}", exc_info=True)
            # 只有真正的未处理异常才返回500
            return JSONResponse(status_code=500, content={"detail": "内部服务器错误"})
```

### 关键特性

1. **HTTPException 透传**：手动抛出的 `HTTPException` 会直接返回给前端，保持原有的状态码和错误消息
2. **自动错误记录**：未处理的异常会自动记录到日志中，包含完整的堆栈信息
3. **统一错误格式**：所有错误都以相同的JSON格式返回

## 使用方式

### 修改前（重复的异常处理）

```python
@router.post("/send-auth-code")
async def send_auth_code(*, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)) -> dict:
    try:
        result = await sms_auth_service.send_auth_code(phone_in.phone, db)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"发送验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e
```

### 修改后（简洁的业务逻辑）

```python
@router.post("/send-auth-code")
async def send_auth_code(*, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)) -> dict:
    """发送登录/注册验证码（统一接口）"""
    # 异常处理由 ExceptionHandlerMiddleware 统一处理
    result = await sms_auth_service.send_auth_code(phone_in.phone, db)
    return result
```

## 异常处理规则

### 1. HTTPException - 直接透传

当业务逻辑需要返回特定的错误码和消息时，直接抛出 `HTTPException`：

```python
# 在服务层或接口层
if not user:
    raise HTTPException(status_code=404, detail="用户不存在")

if phone_already_registered:
    raise HTTPException(status_code=400, detail="该手机号已注册，请直接登录")
```

这些异常会直接返回给前端，不会被转换为500错误。

### 2. ValueError - 自动转换为400错误

参数验证错误会自动转换为400错误：

```python
# 在服务层
def validate_phone_number(phone: str) -> str:
    if not re.match(r'^1[3-9]\d{9}$', phone):
        raise ValueError("手机号格式不正确")  # 自动转换为400错误
    return phone
```

### 3. 其他异常 - 自动转换为500错误

未处理的异常会自动记录日志并返回500错误：

```python
# 数据库连接错误、网络错误等会自动处理
result = await db.execute(query)  # 如果数据库连接失败，自动返回500
```

## 自定义异常处理

### 方案1：在服务层转换异常

```python
class UserService:
    async def get_user_by_phone(self, db: AsyncSession, phone: str) -> User:
        try:
            result = await db.execute(select(User).where(User.username == phone))
            user = result.scalar_one_or_none()
            if not user:
                raise HTTPException(status_code=404, detail="用户不存在")
            return user
        except SQLAlchemyError as e:
            logger.error(f"数据库查询失败: {e}")
            raise HTTPException(status_code=500, detail="数据库服务暂时不可用") from e
```

### 方案2：创建自定义异常类

```python
class PhoneAlreadyRegisteredError(Exception):
    """手机号已注册异常"""
    def __init__(self, phone: str):
        self.phone = phone
        super().__init__(f"手机号 {phone} 已注册")

# 在中间件中添加自定义异常处理
# 或者在服务层捕获并转换为HTTPException
```

## 最佳实践

### 1. API接口层

- **专注业务逻辑**：不要在接口层添加异常处理代码
- **信任中间件**：让中间件处理所有异常
- **明确文档**：在接口文档中说明可能的错误码

```python
@router.post("/api-endpoint")
async def api_endpoint(data: RequestModel) -> ResponseModel:
    """
    API接口描述
    
    Raises:
        HTTPException: 
            - 400: 参数错误
            - 404: 资源不存在
            - 500: 服务器内部错误
    """
    # 只写业务逻辑，不写异常处理
    result = await service.process(data)
    return result
```

### 2. 服务层

- **主动抛出HTTPException**：对于已知的业务错误
- **记录关键信息**：在抛出异常前记录必要的上下文信息
- **转换底层异常**：将底层异常转换为业务异常

```python
class SomeService:
    async def process_data(self, data: dict) -> dict:
        # 参数验证
        if not data.get("phone"):
            raise HTTPException(status_code=400, detail="手机号不能为空")
        
        # 业务逻辑
        try:
            result = await external_api.call(data)
            return result
        except ExternalAPIError as e:
            logger.error(f"外部API调用失败: {e}")
            raise HTTPException(status_code=503, detail="外部服务暂时不可用") from e
```

### 3. 错误消息

- **用户友好**：错误消息应该对用户有意义
- **不暴露内部信息**：不要在错误消息中暴露内部实现细节
- **国际化考虑**：考虑错误消息的国际化需求

```python
# 好的错误消息
raise HTTPException(status_code=400, detail="手机号格式不正确")
raise HTTPException(status_code=404, detail="用户不存在")

# 不好的错误消息
raise HTTPException(status_code=500, detail="SQLAlchemy connection failed")
raise HTTPException(status_code=400, detail="Invalid input in field user.phone.value")
```

## 总结

通过使用统一异常处理中间件，我们实现了：

1. **代码简化**：API接口代码更简洁，专注业务逻辑
2. **一致性**：所有接口的异常处理行为一致
3. **可维护性**：异常处理逻辑集中管理，易于维护
4. **正确性**：手动抛出的HTTPException能正确返回给前端

这种方式大大减少了重复代码，提高了开发效率和代码质量。
