# 微信账号绑定逻辑说明

## 概述

本文档详细说明了微信账号与用户名绑定的逻辑实现，包括各种场景的处理方式和API接口说明。

## API 接口

### 1. `/api/v1/auth/bind-wechat` (推荐)

**功能**: 微信账号与用户名绑定的主要接口

**参数**:
- `openid` (string): 微信用户的OpenID
- `username` (string): 用户名（手机号）
- `code` (string): 短信验证码

### 2. `/api/v1/auth/register-with-wechat` (兼容)

**功能**: 保持向后兼容的注册接口，内部调用 `bind-wechat`

**参数**: 与 `bind-wechat` 相同

## 绑定逻辑详解

### 前置验证

1. **手机号格式验证**: 验证 `username` 是否为有效的手机号格式
2. **短信验证码验证**: 验证提供的验证码是否正确
3. **微信用户信息获取**: 通过 `openid` 从微信API获取用户基本信息

### 场景处理

#### 场景1: OpenID已绑定其他用户名

**条件**: `existing_user_by_openid.username != username`

**处理**: 返回400错误，提示该微信账号已绑定其他用户名

**错误信息**: "该微信账号已绑定用户名: {existing_username}"

#### 场景2: 用户名已绑定其他OpenID

**条件**: `existing_user_by_username.wechat_openid != openid`

**处理**: 返回400错误，提示该用户名已绑定其他微信账号

**错误信息**: "该用户名已绑定其他微信账号"

#### 场景3: 用户名存在但未绑定微信

**条件**: `existing_user_by_username` 存在且 `wechat_openid` 为空

**处理**: 
- 更新现有用户的微信信息
- 设置 `login_type` 为 "wechat"
- 如果用户没有昵称，使用微信昵称

**返回消息**: "微信绑定成功"

#### 场景4: OpenID和用户名都已存在且匹配

**条件**: `existing_user_by_openid.username == username`

**处理**: 
- 更新微信用户信息（unionid、昵称、头像）
- 保持其他信息不变

**返回消息**: "微信信息更新成功"

#### 场景5: 都不存在，创建新用户

**条件**: 既没有匹配的用户名，也没有匹配的OpenID

**处理**: 
- 创建新用户记录
- 设置所有微信相关字段
- 设置 `login_type` 为 "wechat"
- 设置 `is_active` 为 `True`

**返回消息**: "注册成功"

## 数据库字段说明

### User 模型相关字段

```python
# 基本信息
username: str          # 用户名（手机号）
nickname: str          # 用户昵称
login_type: str        # 登录方式："password" 或 "wechat"
is_active: bool        # 账号是否激活

# 微信相关字段
wechat_openid: str     # 微信OpenID（唯一）
wechat_unionid: str    # 微信UnionID（可选）
wechat_nickname: str   # 微信昵称
wechat_avatar: str     # 微信头像URL
```

## 返回数据格式

### 成功响应

```json
{
  "message": "操作结果消息",
  "user": {
    "id": 123,
    "username": "13800138000",
    "nickname": "用户昵称",
    "wechat_nickname": "微信昵称",
    "avatar": "微信头像URL"
  },
  "access_token": "JWT令牌",
  "token_type": "bearer"
}
```

### 错误响应

```json
{
  "detail": "错误详细信息"
}
```

## 安全考虑

1. **验证码验证**: 必须通过短信验证码验证，确保手机号归属
2. **唯一性约束**: OpenID和用户名都有唯一性约束，防止重复绑定
3. **事务处理**: 所有数据库操作都在事务中进行，确保数据一致性
4. **错误回滚**: 发生异常时自动回滚数据库事务

## 使用示例

### 新用户注册

```bash
curl -X POST "http://localhost:8000/api/v1/auth/bind-wechat" \
  -d "openid=wx_openid_123" \
  -d "username=13800138000" \
  -d "code=123456"
```

### 现有用户绑定微信

```bash
curl -X POST "http://localhost:8000/api/v1/auth/bind-wechat" \
  -d "openid=wx_openid_456" \
  -d "username=13900139000" \
  -d "code=654321"
```

## 测试

运行测试脚本验证各种场景：

```bash
python test_wechat_bind.py
```

## 注意事项

1. **开发模式**: 在开发模式下，可以使用万能验证码 "000000"
2. **向后兼容**: 保留了原有的 `/register-with-wechat` 接口
3. **推荐使用**: 新的集成应该使用 `/bind-wechat` 接口
4. **错误处理**: 所有错误都会返回适当的HTTP状态码和错误信息