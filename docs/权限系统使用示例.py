#!/usr/bin/env python3
"""
统一权限系统使用示例

展示如何在实际项目中使用新的统一权限系统
"""


from fastapi import Depends, FastAPI, HTTPException, status
from sqlalchemy.orm import Session

from app.api.permission_deps import PermissionDeps

# 导入统一权限系统
from app.core.permission_system import (
    Action,
    Permission,
    PermissionChecker,
    Permissions,
    ResourceType,
    Scope,
    get_permission_from_string,
)
from app.models.article import Article
from app.models.user import User
from app.services.unified_permission_service import UnifiedPermissionService

# ============================================================================
# 1. 在 FastAPI 路由中使用权限检查
# ============================================================================

app = FastAPI()

# 方式1：使用预定义权限常量
@app.get("/users")
def get_users(
    current_user: User = Depends(PermissionDeps.require_permission(Permissions.USER_READ_ALL))
):
    """获取用户列表 - 需要用户读取权限"""
    return {"message": "用户列表"}


# 方式2：使用字符串权限（兼容旧系统）
@app.post("/articles")
def create_article(
    current_user: User = Depends(PermissionDeps.require_permission("articles:create"))
):
    """创建文章 - 使用字符串权限"""
    return {"message": "文章已创建"}


# 方式3：使用资源级权限检查
@app.put("/articles/{article_id}")
def update_article(
    article_id: int,
    current_user: User = Depends(PermissionDeps.require_resource_permission(
        ResourceType.ARTICLE, Action.UPDATE, Scope.OWN
    ))
):
    """更新文章 - 需要文章更新权限"""
    return {"message": f"文章 {article_id} 已更新"}


# 方式4：多权限检查（满足其中一个即可）
@app.get("/admin/dashboard")
def admin_dashboard(
    current_user: User = Depends(PermissionDeps.require_permissions([
        Permissions.SYSTEM_MANAGE,
        Permissions.USER_MANAGE,
        "admin:dashboard"  # 兼容旧格式
    ]))
):
    """管理员仪表板 - 需要管理权限之一"""
    return {"message": "管理员仪表板"}


# ============================================================================
# 2. 在服务层中使用权限检查
# ============================================================================

class ArticleService:
    """文章服务示例"""
    
    def __init__(self, db: Session):
        self.db = db
        self.permission_service = UnifiedPermissionService(db)
    
    def get_article(self, article_id: int, user: User | None = None) -> Article:
        """获取文章 - 带权限检查"""
        # 从数据库获取文章
        article = self.db.query(Article).filter(Article.id == article_id).first()
        if not article:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        # 检查读取权限
        if not self.permission_service.check_article_access(user, article, Action.READ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此文章"
            )
        
        return article
    
    def update_article(self, article_id: int, data: dict, user: User) -> Article:
        """更新文章 - 带权限检查"""
        article = self.db.query(Article).filter(Article.id == article_id).first()
        if not article:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        # 检查更新权限
        self.permission_service.require_article_access(user, article, Action.UPDATE)
        
        # 执行更新逻辑
        for key, value in data.items():
            setattr(article, key, value)
        
        self.db.commit()
        return article
    
    def delete_article(self, article_id: int, user: User) -> bool:
        """删除文章 - 带权限检查"""
        article = self.db.query(Article).filter(Article.id == article_id).first()
        if not article:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        # 检查删除权限
        self.permission_service.require_article_access(user, article, Action.DELETE)
        
        # 执行删除逻辑
        self.db.delete(article)
        self.db.commit()
        return True
    
    def get_user_articles(self, user: User, category_id: int | None = None) -> list:
        """获取用户可访问的文章列表"""
        # 使用批量权限检查
        accessible_ids = self.permission_service.get_accessible_articles_by_category(
            user, category_id
        )
        
        # 根据ID列表查询文章
        articles = self.db.query(Article).filter(Article.id.in_(accessible_ids)).all()
        return articles


# ============================================================================
# 3. 直接使用权限检查器
# ============================================================================

def check_user_permissions_example(user: User):
    """直接使用权限检查器的示例"""
    
    # 检查单个权限
    can_create_article = PermissionChecker.check_permission(
        user, Permissions.ARTICLE_CREATE
    )
    print(f"用户可以创建文章: {can_create_article}")
    
    # 检查资源权限
    article = Article(id=1, author_id=user.id, is_published=True, is_approved=True)
    can_update_own_article = PermissionChecker.check_permission(
        user, 
        Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.OWN),
        article
    )
    print(f"用户可以更新自己的文章: {can_update_own_article}")
    
    # 检查旧格式权限
    legacy_permission = get_permission_from_string("users:list")
    can_list_users = PermissionChecker.check_permission(user, legacy_permission)
    print(f"用户可以列出用户: {can_list_users}")


# ============================================================================
# 4. 权限缓存和配置管理
# ============================================================================

def permission_management_example():
    """权限管理示例"""
    from app.core.permission_system import (
        clear_all_permissions_cache,
        clear_user_permissions_cache,
        update_permission_config,
    )
    
    # 清除特定用户的权限缓存
    user_id = 123
    clear_user_permissions_cache(user_id)
    print(f"已清除用户 {user_id} 的权限缓存")
    
    # 清除所有权限缓存
    clear_all_permissions_cache()
    print("已清除所有权限缓存")
    
    # 更新权限配置
    update_permission_config(
        cache_enabled=True,
        cache_ttl=600,  # 10分钟
        audit_enabled=True
    )
    print("权限配置已更新")


# ============================================================================
# 5. 获取用户权限摘要
# ============================================================================

def get_user_permissions_summary_example(user: User, db: Session):
    """获取用户权限摘要示例"""
    permission_service = UnifiedPermissionService(db)
    summary = permission_service.get_user_permissions_summary(user)
    
    print(f"用户角色: {summary['role']}")
    print(f"可创建文章: {summary['can_create_article']}")
    print(f"可创建视频: {summary['can_create_video']}")
    print(f"可管理用户: {summary['can_manage_users']}")
    print(f"可管理系统: {summary['can_manage_system']}")
    print(f"权限列表: {summary['permissions'][:5]}...")  # 显示前5个权限
    
    return summary


# ============================================================================
# 6. 自定义权限检查
# ============================================================================

def custom_permission_check_example(user: User, article: Article):
    """自定义权限检查示例"""
    from datetime import datetime, timedelta
    
    # 创建带条件的临时权限
    temp_permission = Permission(
        resource=ResourceType.ARTICLE,
        action=Action.UPDATE,
        scope=Scope.OWN,
        conditions={"status": "draft"},  # 只能编辑草稿
        expires_at=datetime.now() + timedelta(hours=24),  # 24小时后过期
        description="临时编辑权限"
    )
    
    # 检查权限
    can_edit = PermissionChecker.check_permission(user, temp_permission, article)
    print(f"用户可以编辑草稿文章: {can_edit}")
    
    # 检查权限是否过期
    is_expired = temp_permission.is_expired()
    print(f"权限是否过期: {is_expired}")


# ============================================================================
# 7. 错误处理示例
# ============================================================================

def error_handling_example(user: User):
    """错误处理示例"""
    try:
        # 要求用户拥有系统管理权限
        PermissionChecker.require_permission(user, Permissions.SYSTEM_MANAGE)
        print("用户拥有系统管理权限")
    except HTTPException as e:
        print(f"权限检查失败: {e.detail}")
    
    try:
        # 要求用户拥有资源访问权限
        PermissionChecker.require_resource_access(
            user, ResourceType.USER, Action.DELETE, is_public=False
        )
        print("用户拥有删除用户权限")
    except HTTPException as e:
        print(f"资源权限检查失败: {e.detail}")


if __name__ == "__main__":
    print("统一权限系统使用示例")
    print("请参考代码中的各种使用方式")
    print("\n主要特性:")
    print("1. FastAPI 路由权限检查")
    print("2. 服务层权限验证")
    print("3. 直接权限检查器使用")
    print("4. 权限缓存管理")
    print("5. 用户权限摘要")
    print("6. 自定义权限检查")
    print("7. 完善的错误处理")