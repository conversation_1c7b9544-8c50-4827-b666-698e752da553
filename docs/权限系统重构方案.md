# 权限系统重构方案

## 问题分析

当前项目存在两套权限系统，导致代码混乱和维护困难：

### 旧权限系统（基于字符串）
- 位置：`app/api/deps.py` 中的 `check_permissions`
- 位置：`app/core/permissions.py` 中的枚举和函数
- 特点：使用字符串形式的权限代码（如 "users:list", "articles:create"）
- 使用场景：主要在 roles.py 和 users.py 等管理接口中使用

### 新权限系统（基于数据类）
- 位置：`app/core/permission_system.py` 中的 Permission 数据类
- 位置：`app/api/permission_deps.py` 中的依赖函数
- 特点：使用结构化的 Permission(ResourceType, Action, Scope) 形式
- 使用场景：设计完整但实际使用较少

### 混合使用的权限服务
- 文章和视频权限服务使用自定义的权限检查逻辑
- 没有统一的权限检查标准
- 代码重复，维护困难

## 统一权限系统设计

### 1. 核心架构

```
权限系统架构
├── 权限定义层 (Permission Definition)
│   ├── 资源类型 (ResourceType)
│   ├── 操作类型 (Action) 
│   ├── 权限范围 (Scope)
│   └── 权限组合 (Permission)
├── 权限检查层 (Permission Checker)
│   ├── 基础权限检查
│   ├── 资源所有权检查
│   └── 上下文权限检查
├── 权限依赖层 (Permission Dependencies)
│   ├── FastAPI 依赖函数
│   ├── 装饰器支持
│   └── 中间件集成
└── 权限服务层 (Permission Services)
    ├── 用户权限服务
    ├── 资源权限服务
    └── 权限缓存服务
```

### 2. 权限模型设计

#### 2.1 基础权限模型
```python
@dataclass
class Permission:
    resource: ResourceType  # 资源类型
    action: Action         # 操作类型
    scope: Scope          # 权限范围
    conditions: Optional[Dict[str, Any]] = None  # 额外条件
```

#### 2.2 资源类型枚举
```python
class ResourceType(str, Enum):
    USER = "user"
    ARTICLE = "article"
    VIDEO = "video"
    COMMENT = "comment"
    FOLDER = "folder"
    ROLE = "role"
    PERMISSION = "permission"
```

#### 2.3 操作类型枚举
```python
class Action(str, Enum):
    READ = "read"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"  # 管理权限（包含所有操作）
    APPROVE = "approve"  # 审核权限
    PUBLISH = "publish"  # 发布权限
```

#### 2.4 权限范围枚举
```python
class Scope(str, Enum):
    OWN = "own"      # 仅自己的资源
    ALL = "all"      # 所有资源
    PUBLIC = "public"  # 公开资源
    DEPARTMENT = "department"  # 部门资源（扩展）
```

### 3. 权限检查器设计

#### 3.1 统一权限检查接口
```python
class PermissionChecker:
    @staticmethod
    def check_permission(user: User, permission: Permission, resource: Any = None) -> bool:
        """统一权限检查入口"""
        
    @staticmethod
    def require_permission(user: User, permission: Permission, resource: Any = None) -> None:
        """权限检查，失败抛出异常"""
        
    @staticmethod
    def check_resource_access(user: User, resource_type: ResourceType, 
                            action: Action, resource: Any) -> bool:
        """资源访问权限检查"""
```

#### 3.2 权限检查策略
1. **超级管理员策略**：超级管理员拥有所有权限
2. **角色权限策略**：基于用户角色的权限检查
3. **资源所有权策略**：检查用户是否为资源所有者
4. **公开资源策略**：检查资源是否为公开状态
5. **条件权限策略**：基于额外条件的权限检查

### 4. FastAPI 依赖函数设计

#### 4.1 基础权限依赖
```python
def require_permission(permission: Permission) -> Callable:
    """要求单个权限的依赖函数"""
    
def require_permissions(permissions: List[Permission]) -> Callable:
    """要求多个权限的依赖函数"""
    
def require_any_permission(permissions: List[Permission]) -> Callable:
    """要求任一权限的依赖函数"""
```

#### 4.2 资源权限依赖
```python
def require_resource_permission(resource_type: ResourceType, action: Action) -> Callable:
    """要求资源权限的依赖函数"""
    
def optional_resource_permission(resource_type: ResourceType, action: Action) -> Callable:
    """可选资源权限的依赖函数（支持游客访问）"""
```

#### 4.3 预定义权限依赖
```python
class PermissionDeps:
    # 用户管理
    user_list = require_permission(Permission(ResourceType.USER, Action.READ, Scope.ALL))
    user_create = require_permission(Permission(ResourceType.USER, Action.CREATE, Scope.ALL))
    
    # 内容管理
    article_create = require_permission(Permission(ResourceType.ARTICLE, Action.CREATE, Scope.OWN))
    article_manage = require_permission(Permission(ResourceType.ARTICLE, Action.MANAGE, Scope.ALL))
```

### 5. 权限服务层设计

#### 5.1 用户权限服务
```python
class UserPermissionService:
    @staticmethod
    async def get_user_permissions(db: AsyncSession, user: User) -> Set[Permission]:
        """获取用户所有权限"""
        
    @staticmethod
    async def check_user_permission(db: AsyncSession, user: User, permission: Permission) -> bool:
        """检查用户是否拥有指定权限"""
```

#### 5.2 资源权限服务
```python
class ResourcePermissionService:
    @staticmethod
    async def check_resource_access(db: AsyncSession, user: User, resource_type: ResourceType,
                                  resource_id: int, action: Action) -> bool:
        """检查用户对特定资源的访问权限"""
        
    @staticmethod
    async def get_accessible_resources(db: AsyncSession, user: User, 
                                     resource_type: ResourceType) -> List[Any]:
        """获取用户可访问的资源列表"""
```

### 6. 迁移策略

#### 6.1 阶段一：统一权限定义
1. 保留现有的两套权限系统
2. 创建新的统一权限系统
3. 提供兼容性适配器

#### 6.2 阶段二：逐步迁移
1. 新功能使用新权限系统
2. 逐步迁移现有接口
3. 保持向后兼容

#### 6.3 阶段三：完全统一
1. 移除旧权限系统
2. 清理冗余代码
3. 优化性能

### 7. 实施计划

#### 第一步：创建统一权限系统核心
- [ ] 重构 `app/core/permission_system.py`
- [ ] 创建统一的权限检查器
- [ ] 实现权限依赖函数

#### 第二步：创建兼容性适配器
- [ ] 创建字符串权限到新权限的转换器
- [ ] 保持现有接口的兼容性

#### 第三步：迁移核心模块
- [ ] 迁移用户管理接口
- [ ] 迁移内容管理接口
- [ ] 更新权限服务

#### 第四步：优化和清理
- [ ] 移除旧权限系统
- [ ] 优化权限检查性能
- [ ] 添加权限缓存

### 8. 配置和扩展

#### 8.1 权限配置
```python
# 权限配置文件
PERMISSION_CONFIG = {
    "cache_enabled": True,
    "cache_ttl": 300,  # 5分钟
    "strict_mode": True,  # 严格模式
    "audit_enabled": True,  # 审计日志
}
```

#### 8.2 权限扩展
- 支持动态权限
- 支持时间限制权限
- 支持条件权限
- 支持权限继承

### 9. 测试策略

#### 9.1 单元测试
- 权限检查器测试
- 权限依赖函数测试
- 权限服务测试

#### 9.2 集成测试
- API 权限测试
- 端到端权限测试
- 性能测试

#### 9.3 安全测试
- 权限绕过测试
- 权限提升测试
- 边界条件测试

## 总结

这个统一权限系统设计方案将：

1. **解决当前问题**：统一两套权限系统，消除代码冲突
2. **提高可维护性**：统一的权限检查逻辑，减少代码重复
3. **增强扩展性**：支持更复杂的权限需求
4. **保证兼容性**：平滑迁移，不影响现有功能
5. **提升性能**：权限缓存和优化的检查逻辑

通过分阶段实施，可以在不影响现有系统稳定性的前提下，逐步完成权限系统的统一和优化。