# 点赞和收藏功能文档

## 功能概述

本项目已成功实现了文章和视频的点赞和收藏功能，包括历史记录追踪和Redis缓存优化。

## 主要特性

### ✅ 已实现功能

1. **点赞功能**
   - 用户可以对文章和视频进行点赞/取消点赞
   - 支持点赞数量统计
   - 防重复点赞（同一用户对同一内容只能点赞一次）
   - 软删除机制（支持恢复点赞状态）

2. **收藏功能**
   - 用户可以收藏/取消收藏文章和视频
   - 支持收藏备注功能
   - 收藏数量统计
   - 防重复收藏

3. **历史记录**
   - 用户点赞历史查询（支持分页和筛选）
   - 用户收藏历史查询（支持分页和筛选）
   - 收藏历史包含内容详情
   - 用户活动统计

4. **性能优化**
   - Redis缓存点赞/收藏数量
   - Redis缓存用户点赞/收藏状态
   - 批量查询优化
   - 热点内容缓存

## 数据库设计

### 点赞表 (likes)
```sql
CREATE TABLE likes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    content_type VARCHAR(20) NOT NULL,  -- 'article' 或 'video'
    content_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, content_type, content_id)
);
```

### 收藏表 (favorites)
```sql
CREATE TABLE favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    content_type VARCHAR(20) NOT NULL,  -- 'article' 或 'video'
    content_id INTEGER NOT NULL,
    note TEXT,                          -- 收藏备注
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id, content_type, content_id)
);
```

## API 端点

### 点赞相关 API

#### 1. 切换点赞状态
```http
POST /api/v1/likes/toggle
Content-Type: application/json

{
    "content_type": "article",
    "content_id": 1
}
```

#### 2. 获取点赞状态
```http
GET /api/v1/likes/status?content_type=article&content_id=1
```

#### 3. 获取点赞历史
```http
GET /api/v1/likes/history?content_type=article&skip=0&limit=20
```

#### 4. 批量获取点赞信息
```http
POST /api/v1/likes/batch-status
Content-Type: application/json

[
    {"content_type": "article", "content_id": 1},
    {"content_type": "video", "content_id": 2}
]
```

### 收藏相关 API

#### 1. 切换收藏状态
```http
POST /api/v1/favorites/toggle
Content-Type: application/json

{
    "content_type": "article",
    "content_id": 1,
    "note": "很有用的文章"
}
```

#### 2. 获取收藏状态
```http
GET /api/v1/favorites/status?content_type=article&content_id=1
```

#### 3. 获取收藏历史
```http
GET /api/v1/favorites/history?content_type=article&skip=0&limit=20
```

#### 4. 更新收藏备注
```http
PUT /api/v1/favorites/{favorite_id}/note
Content-Type: application/json

{
    "note": "更新后的备注"
}
```

### 用户历史记录 API

#### 1. 获取用户点赞历史
```http
GET /api/v1/user/history/likes?content_type=article&skip=0&limit=20
```

#### 2. 获取用户收藏历史
```http
GET /api/v1/user/history/favorites?content_type=video&skip=0&limit=20
```

#### 3. 获取收藏历史（包含内容详情）
```http
GET /api/v1/user/history/favorites/with-content?skip=0&limit=20
```

#### 4. 获取用户活动统计
```http
GET /api/v1/user/history/stats
```

#### 5. 获取用户最近活动
```http
GET /api/v1/user/history/recent-activity?limit=10
```

### 扩展的文章/视频 API

#### 1. 获取包含统计信息的文章列表
```http
GET /api/v1/articles/with-stats?skip=0&limit=20
```

#### 2. 获取包含统计信息的文章详情
```http
GET /api/v1/articles/{article_id}/with-stats
```

## 部署和使用

### 1. 数据库迁移

首次部署时，需要运行数据库迁移脚本：

```bash
python migrate_database.py
```

### 2. Redis 配置

确保 Redis 服务正在运行，并在 `.env` 文件中配置：

```env
REDIS_URL=redis://localhost:6379/0
```

### 3. 启动服务

```bash
uvicorn app.main:app --reload
```

## 缓存策略

### Redis 缓存键设计

1. **点赞数量缓存**: `like_count:article:123`
2. **用户点赞状态**: `user_like:456:article:123`
3. **用户点赞集合**: `user_like_set:456:article`
4. **收藏数量缓存**: `favorite_count:video:789`
5. **用户收藏状态**: `user_favorite:456:video:789`
6. **热点内容**: `hot_content:article`

### 缓存过期时间

- 普通缓存：1小时 (3600秒)
- 热点数据缓存：30分钟 (1800秒)

## 性能优化建议

1. **批量查询**: 使用批量API减少数据库查询次数
2. **缓存预热**: 对热门内容进行缓存预热
3. **异步处理**: 考虑将统计更新改为异步处理
4. **数据库索引**: 已创建必要的数据库索引

## 监控和维护

1. **监控Redis缓存命中率**
2. **定期清理无效缓存**
3. **监控数据库查询性能**
4. **定期备份点赞和收藏数据**

## 扩展建议

1. **点赞通知**: 实现点赞通知功能
2. **收藏分类**: 支持收藏夹分类管理
3. **社交功能**: 查看好友的点赞和收藏
4. **数据分析**: 内容热度分析和推荐算法
