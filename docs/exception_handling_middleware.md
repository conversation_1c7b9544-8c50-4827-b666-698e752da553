# 异常处理中间件使用指南

## 概述

为了解决后端异常被过度捕获导致前端无法接收到具体错误信息的问题，我们引入了统一的异常处理中间件 `ExceptionHandlerMiddleware`。

## 问题背景

之前的代码中存在以下问题：

```python
# ❌ 错误的异常处理方式
try:
    # 业务逻辑
    if some_condition:
        raise HTTPException(status_code=400, detail="具体错误信息")
except Exception as e:
    logger.error(f"操作失败：{str(e)}")
    raise HTTPException(status_code=500, detail="操作失败，请稍后重试") from e
```

这种方式会将所有异常（包括有意抛出的 `HTTPException`）都转换为 500 错误，导致前端无法获取具体的错误信息。

## 解决方案

### 1. 异常处理中间件

在 `app/api/middleware.py` 中新增了 `ExceptionHandlerMiddleware`：

```python
class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """统一异常处理中间件"""

    async def dispatch(self, request: Request, call_next) -> Any:
        try:
            response = await call_next(request)
            return response
        except HTTPException as http_exc:
            # 直接返回HTTPException，不进行转换
            return JSONResponse(
                status_code=http_exc.status_code,
                content={"detail": http_exc.detail}
            )
        except Exception as e:
            # 只有真正的未处理异常才返回500
            return JSONResponse(
                status_code=500,
                content={"detail": "内部服务器错误"}
            )
```

### 2. 中间件注册

在 `app/main.py` 中将异常处理中间件注册为第一个中间件：

```python
# 添加异常处理中间件（最先执行，确保捕获所有异常）
app.add_middleware(ExceptionHandlerMiddleware)
```

### 3. 端点代码简化

现在端点函数中的异常处理可以大大简化：

```python
# ✅ 正确的异常处理方式
async def some_endpoint(...):
    try:
        # 验证手机号格式
        phone = validate_phone_number(request.phone)
        
        # 业务逻辑
        if some_condition:
            raise HTTPException(status_code=400, detail="具体错误信息")
            
        return {"result": "success"}
    except ValueError as e:
        # 只捕获特定的已知异常
        raise HTTPException(status_code=400, detail=str(e)) from e
    # 不再需要 except Exception 块
```

## 最佳实践

### 1. 异常处理原则

- **只捕获你能处理的异常**：不要使用 `except Exception` 来捕获所有异常
- **让 HTTPException 自然传播**：不要捕获并重新抛出 HTTPException
- **使用具体的异常类型**：如 `ValueError`、`KeyError` 等
- **保留异常链**：使用 `from e` 保留原始异常信息

### 2. 推荐的异常处理模式

```python
# 模式1：只处理特定异常
async def endpoint_function(...):
    try:
        # 业务逻辑
        result = some_operation()
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"参数错误: {str(e)}") from e
    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"缺少必要参数: {str(e)}") from e

# 模式2：需要重新抛出HTTPException的情况
async def endpoint_function(...):
    try:
        # 业务逻辑
        result = some_operation()
        return result
    except HTTPException:
        # 直接重新抛出，不做任何处理
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
```

### 3. 避免的反模式

```python
# ❌ 不要这样做
try:
    # 业务逻辑
    pass
except Exception as e:
    logger.error(f"错误: {e}")
    raise HTTPException(status_code=500, detail="操作失败") from e

# ❌ 不要捕获并重新包装HTTPException
try:
    if condition:
        raise HTTPException(status_code=400, detail="具体错误")
except HTTPException as e:
    raise HTTPException(status_code=500, detail="操作失败") from e
```

## 测试

使用提供的测试脚本验证异常处理是否正常工作：

```bash
python test_exception_middleware.py
```

## 效果

使用异常处理中间件后：

1. **400 错误正确传递**：前端能收到具体的错误信息
2. **500 错误统一处理**：真正的服务器错误会被统一处理
3. **代码更简洁**：端点函数中不需要复杂的异常处理逻辑
4. **日志更清晰**：异常信息在中间件中统一记录

## 注意事项

1. **中间件顺序很重要**：异常处理中间件必须是第一个注册的中间件
2. **保持向后兼容**：现有的异常处理逻辑仍然有效
3. **日志记录**：可以在中间件中添加统一的日志记录逻辑
4. **监控集成**：可以在中间件中集成错误监控和报警