# 统一登录注册API文档

## 概述

我们已经成功合并了登录和注册的逻辑，实现了当用户不存在时自动注册并登录的功能。新的统一接口提供了更好的用户体验，同时保持了向后兼容性。

## 新增的统一接口

### 1. 发送验证码（统一接口）

**接口地址：** `POST /api/v1/sms-auth/send-auth-code`

**功能：** 发送登录/注册验证码，自动检测用户状态

**请求参数：**
```json
{
  "phone": "13800138000"
}
```

**响应示例：**
```json
{
  "message": "验证码已发送",
  "is_registered": false,
  "action": "register"
}
```

**响应字段说明：**
- `message`: 发送结果消息
- `is_registered`: 用户是否已注册（true=已注册，false=未注册）
- `action`: 建议的操作类型（"login" 或 "register"）

### 2. 验证验证码（统一接口）

**接口地址：** `POST /api/v1/sms-auth/verify-auth-code`

**功能：** 验证验证码，自动注册新用户或登录已有用户

**请求参数：**
```json
{
  "phone": "13800138000",
  "code": "123456"
}
```

**成功响应示例（直接登录）：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "13800138000",
    "nickname": "快乐的小猫123",
    "email": null,
    "avatar": null,
    "is_active": true,
    "is_superuser": false,
    "login_type": "phone",
    "created_at": "2024-01-01T00:00:00",
    "last_login": "2024-01-01T12:00:00"
  }
}
```

**需要设备验证的响应示例：**
```json
{
  "requires_device_verification": true,
  "message": "检测到新设备登录，需要进行设备验证",
  "device_info": {
    "device_name": "Chrome Browser",
    "device_type": "web",
    "location": "北京市",
    "is_new_device": true
  },
  "verification_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## 功能特性

### 1. 自动注册
- 当用户不存在时，系统会自动创建新用户
- 新用户使用随机生成的昵称
- 自动分配默认角色
- 首次注册的设备会被自动信任

### 2. 设备安全
- 保持原有的设备验证机制
- 新设备登录需要额外的短信验证
- 已信任的设备可以直接登录

### 3. 向后兼容
- 保留了原有的 `/send-login-code` 和 `/send-register-code` 接口
- 保留了原有的 `/verify-login-code` 和 `/verify-register-code` 接口
- 现有的客户端代码无需修改

## 使用流程

### 新用户注册并登录流程
1. 调用 `/send-auth-code` 发送验证码
2. 系统返回 `is_registered: false, action: "register"`
3. 调用 `/verify-auth-code` 验证验证码
4. 系统自动创建用户并返回登录令牌

### 已有用户登录流程
1. 调用 `/send-auth-code` 发送验证码
2. 系统返回 `is_registered: true, action: "login"`
3. 调用 `/verify-auth-code` 验证验证码
4. 如果是信任设备，直接返回登录令牌
5. 如果是新设备，返回设备验证信息，需要进行设备验证

## 错误处理

### 常见错误码
- `400`: 请求参数错误（手机号格式错误、验证码错误等）
- `500`: 服务器内部错误

### 错误响应示例
```json
{
  "detail": "验证码错误"
}
```

## 测试

我们提供了测试脚本 `test_unified_auth.py` 来验证新功能：

```bash
python test_unified_auth.py
```

测试脚本会：
1. 测试统一的发送验证码接口
2. 测试统一的验证码验证接口
3. 验证自动注册功能
4. 测试向后兼容性

## 配置说明

### 开发环境
- 在开发环境下（`MODE=dev`），验证码会在服务器日志中显示
- 不会实际发送短信

### 生产环境
- 会通过Celery任务发送真实的短信验证码
- 需要配置短信服务提供商的相关参数

## 安全考虑

1. **验证码有效期**：验证码默认5分钟有效期
2. **发送频率限制**：同一手机号1分钟内只能发送一次验证码
3. **错误次数限制**：验证码错误次数过多会要求重新获取
4. **设备验证**：新设备登录需要额外的短信验证
5. **令牌管理**：使用Redis管理令牌，支持令牌撤销

## 总结

新的统一接口简化了客户端的实现逻辑，提供了更好的用户体验：
- 用户无需区分登录和注册操作
- 系统自动处理用户状态
- 保持了完整的安全机制
- 向后兼容现有代码

这个实现符合现代应用的用户体验标准，同时保持了系统的安全性和可维护性。
