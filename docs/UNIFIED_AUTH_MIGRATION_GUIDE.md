# 统一认证返回值迁移指南

## 概述

本指南将帮助您将现有的SMS认证和微信认证代码迁移到新的统一认证返回值系统。

## 迁移步骤

### 1. 后端API迁移

#### 1.1 更新导入语句

**原有代码**:
```python
# SMS认证
from app.api.endpoints.sms_auth import AuthResponse, DeviceVerificationResponse

# 微信认证
from app.schemas.wechat import WeChatBindResponse, LoginStatusResponse
```

**迁移后**:
```python
# 统一认证
from app.schemas.unified_auth import (
    UnifiedAuthResponse,
    UnifiedDeviceVerificationResponse
)
from app.services.unified_auth_adapter import unified_auth_adapter

# 如需向后兼容，保留原有导入
from app.api.endpoints.sms_auth import AuthResponse, DeviceVerificationResponse
from app.schemas.wechat import WeChatBindResponse, LoginStatusResponse
```

#### 1.2 更新接口返回类型

**SMS认证接口迁移**:

```python
# 原有代码
@router.post("/verify-auth-code", response_model=AuthResponse | DeviceVerificationResponse)
async def verify_auth_code(
    *, verify_in: VerifyCode, request: Request, db: AsyncSession = Depends(deps.get_db)
) -> AuthResponse | DeviceVerificationResponse:
    result = await sms_auth_service.handle_auth_verification(
        verify_in.phone, verify_in.code, request, db
    )
    
    if result.requires_device_verification:
        return DeviceVerificationResponse(
            requires_device_verification=True,
            verification_token=result.verification_token,
            device_info=result.device_info,
            message=result.message,
        )
    
    return AuthResponse(
        access_token=result.access_token,
        user=UserInfo.model_validate(result.user),
    )

# 迁移后
@router.post("/verify-auth-code", response_model=UnifiedAuthResponse | UnifiedDeviceVerificationResponse)
async def verify_auth_code(
    *, verify_in: VerifyCode, request: Request, db: AsyncSession = Depends(deps.get_db)
) -> UnifiedAuthResponse | UnifiedDeviceVerificationResponse:
    result = await sms_auth_service.handle_auth_verification(
        verify_in.phone, verify_in.code, request, db
    )
    
    # 转换为统一认证结果
    unified_result = unified_auth_adapter.from_sms_auth_result(result)
    
    if unified_result.requires_device_verification:
        return unified_result.to_device_verification_response()
    
    return unified_result.to_auth_response()
```

**微信认证接口迁移**:

```python
# 原有代码
@router.post("/bind", response_model=WeChatBindResponse, summary="微信账号绑定")
async def bind_wechat(request: WeChatBindRequest, db: AsyncSession = Depends(get_db)):
    # ... 业务逻辑 ...
    
    return WeChatBindResponse(
        message=action_message,
        user={
            "id": user.id,
            "username": user.username,
            "nickname": user.nickname,
            "wechat_nickname": user.wechat_nickname,
            "avatar": user.wechat_avatar,
        },
        access_token=access_token,
        token_type=token_type,
    )

# 迁移后
@router.post("/bind", response_model=UnifiedAuthResponse, summary="微信账号绑定")
async def bind_wechat(request: WeChatBindRequest, db: AsyncSession = Depends(get_db)):
    # ... 业务逻辑 ...
    
    # 构建统一认证响应
    unified_response = UnifiedAuthResponse(
        access_token=access_token,
        token_type=token_type,
        user=user,
        message=action_message,
        auth_method="wechat"
    )
    return unified_response
```

### 2. 前端代码迁移

#### 2.1 更新类型定义

**原有代码**:
```typescript
// SMS认证响应
interface SmsAuthResponse {
  access_token: string;
  token_type: string;
  user: {
    id: number;
    username: string;
    nickname?: string;
    email?: string;
    avatar?: string;
    is_active: boolean;
    last_login?: string;
    created_at: string;
  };
}

// 微信认证响应
interface WeChatBindResponse {
  message: string;
  user: {
    id: number;
    username: string;
    nickname?: string;
    wechat_nickname?: string;
    avatar?: string;
  };
  access_token: string;
  token_type: string;
}
```

**迁移后**:
```typescript
// 统一认证响应
interface UnifiedAuthResponse {
  access_token: string;
  token_type: string;
  user: {
    id: number;
    username: string;
    nickname?: string;
    email?: string;
    avatar?: string;
    is_active: boolean;
    last_login?: string;
    created_at: string;
    wechat_nickname?: string;
    wechat_avatar?: string;
    login_type?: string;
  };
  message?: string;
  auth_method: 'sms' | 'wechat';
}

// 统一设备验证响应
interface UnifiedDeviceVerificationResponse {
  requires_device_verification: boolean;
  message: string;
  device_info?: any;
  verification_token?: string;
  auth_method: 'sms' | 'wechat';
}
```

#### 2.2 统一处理函数

**原有代码**:
```typescript
// SMS登录处理
function handleSmsLogin(response: SmsAuthResponse) {
  localStorage.setItem('access_token', response.access_token);
  localStorage.setItem('user_info', JSON.stringify(response.user));
  showSuccessMessage('登录成功');
  router.push('/dashboard');
}

// 微信登录处理
function handleWeChatLogin(response: WeChatBindResponse) {
  localStorage.setItem('access_token', response.access_token);
  const userInfo = {
    ...response.user,
    avatar: response.user.avatar || response.user.wechat_nickname
  };
  localStorage.setItem('user_info', JSON.stringify(userInfo));
  showSuccessMessage(response.message);
  router.push('/dashboard');
}
```

**迁移后**:
```typescript
// 统一登录处理
function handleAuthSuccess(response: UnifiedAuthResponse) {
  // 保存token
  localStorage.setItem('access_token', response.access_token);
  
  // 处理用户信息
  const userInfo = {
    ...response.user,
    // 根据认证方式选择合适的头像
    avatar: response.user.wechat_avatar || response.user.avatar,
    // 根据认证方式选择合适的昵称
    displayName: response.user.wechat_nickname || response.user.nickname || response.user.username
  };
  
  localStorage.setItem('user_info', JSON.stringify(userInfo));
  
  // 显示成功消息
  showSuccessMessage(response.message || '登录成功');
  
  // 根据认证方式执行特定逻辑
  if (response.auth_method === 'wechat') {
    trackEvent('wechat_login_success');
  } else if (response.auth_method === 'sms') {
    trackEvent('sms_login_success');
  }
  
  router.push('/dashboard');
}

// 统一设备验证处理
function handleDeviceVerification(response: UnifiedDeviceVerificationResponse) {
  showDeviceVerificationModal({
    message: response.message,
    deviceInfo: response.device_info,
    verificationToken: response.verification_token,
    authMethod: response.auth_method
  });
}
```

#### 2.3 API调用更新

**原有代码**:
```typescript
// SMS验证码登录
async function verifySmsCode(phone: string, code: string) {
  try {
    const response = await api.post<SmsAuthResponse | DeviceVerificationResponse>(
      '/api/sms-auth/verify-auth-code',
      { phone, code }
    );
    
    if ('requires_device_verification' in response.data) {
      handleDeviceVerification(response.data);
    } else {
      handleSmsLogin(response.data);
    }
  } catch (error) {
    handleError(error);
  }
}

// 微信绑定
async function bindWeChat(openid: string, username: string, code: string) {
  try {
    const response = await api.post<WeChatBindResponse>(
      '/api/wechat-auth/bind',
      { openid, username, code }
    );
    
    handleWeChatLogin(response.data);
  } catch (error) {
    handleError(error);
  }
}
```

**迁移后**:
```typescript
// 统一认证处理
async function handleAuth(
  endpoint: string,
  data: any
): Promise<void> {
  try {
    const response = await api.post<UnifiedAuthResponse | UnifiedDeviceVerificationResponse>(
      endpoint,
      data
    );
    
    if ('requires_device_verification' in response.data) {
      handleDeviceVerification(response.data);
    } else {
      handleAuthSuccess(response.data);
    }
  } catch (error) {
    handleError(error);
  }
}

// SMS验证码登录
async function verifySmsCode(phone: string, code: string) {
  return handleAuth('/api/sms-auth/verify-auth-code', { phone, code });
}

// 微信绑定
async function bindWeChat(openid: string, username: string, code: string) {
  return handleAuth('/api/wechat-auth/bind', { openid, username, code });
}
```

### 3. 测试代码迁移

#### 3.1 更新测试用例

**原有代码**:
```python
def test_sms_auth_success():
    response = client.post("/api/sms-auth/verify-auth-code", json={
        "phone": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    assert "access_token" in data
    assert "user" in data
    assert data["user"]["username"] == "13800138000"

def test_wechat_bind_success():
    response = client.post("/api/wechat-auth/bind", json={
        "openid": "wx_openid_123",
        "username": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    assert "access_token" in data
    assert "message" in data
    assert data["user"]["username"] == "13800138000"
```

**迁移后**:
```python
def test_unified_auth_response_structure(response_data):
    """验证统一认证响应结构"""
    assert "access_token" in response_data
    assert "token_type" in response_data
    assert "user" in response_data
    assert "auth_method" in response_data
    
    user = response_data["user"]
    assert "id" in user
    assert "username" in user
    assert "is_active" in user
    assert "created_at" in user

def test_sms_auth_success():
    response = client.post("/api/sms-auth/verify-auth-code", json={
        "phone": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # 验证统一格式
    test_unified_auth_response_structure(data)
    assert data["auth_method"] == "sms"
    assert data["user"]["username"] == "13800138000"

def test_wechat_bind_success():
    response = client.post("/api/wechat-auth/bind", json={
        "openid": "wx_openid_123",
        "username": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # 验证统一格式
    test_unified_auth_response_structure(data)
    assert data["auth_method"] == "wechat"
    assert data["user"]["username"] == "13800138000"
    assert "wechat_nickname" in data["user"]

def test_device_verification_response():
    # 模拟需要设备验证的情况
    response = client.post("/api/sms-auth/verify-auth-code", json={
        "phone": "13800138000",
        "code": "123456"
    })
    
    if response.status_code == 200:
        data = response.json()
        
        if "requires_device_verification" in data:
            # 验证设备验证响应格式
            assert data["requires_device_verification"] is True
            assert "message" in data
            assert "auth_method" in data
            assert "verification_token" in data
        else:
            # 验证正常认证响应格式
            test_unified_auth_response_structure(data)
```

### 4. 向后兼容处理

如果需要保持向后兼容，可以创建适配器端点：

```python
# 向后兼容的SMS认证端点
@router.post("/verify-auth-code-legacy", response_model=AuthResponse | DeviceVerificationResponse)
async def verify_auth_code_legacy(
    *, verify_in: VerifyCode, request: Request, db: AsyncSession = Depends(deps.get_db)
) -> AuthResponse | DeviceVerificationResponse:
    """向后兼容的SMS认证接口"""
    # 调用新的统一接口
    unified_response = await verify_auth_code(verify_in, request, db)
    
    # 转换为旧格式
    if isinstance(unified_response, UnifiedDeviceVerificationResponse):
        return DeviceVerificationResponse(
            requires_device_verification=unified_response.requires_device_verification,
            message=unified_response.message,
            device_info=unified_response.device_info,
            verification_token=unified_response.verification_token
        )
    else:
        return unified_auth_adapter.to_legacy_sms_response(unified_response)

# 向后兼容的微信绑定端点
@router.post("/bind-legacy", response_model=WeChatBindResponse)
async def bind_wechat_legacy(request: WeChatBindRequest, db: AsyncSession = Depends(get_db)):
    """向后兼容的微信绑定接口"""
    # 调用新的统一接口
    unified_response = await bind_wechat(request, db)
    
    # 转换为旧格式
    return unified_auth_adapter.to_legacy_wechat_response(unified_response)
```

### 5. 迁移检查清单

#### 后端检查清单
- [ ] 更新所有认证相关接口的返回类型
- [ ] 导入统一认证模块
- [ ] 使用适配器转换现有认证结果
- [ ] 更新测试用例验证新格式
- [ ] 确保向后兼容性（如需要）

#### 前端检查清单
- [ ] 更新类型定义
- [ ] 统一认证成功处理逻辑
- [ ] 统一设备验证处理逻辑
- [ ] 更新API调用代码
- [ ] 更新测试用例
- [ ] 验证用户体验一致性

#### 测试检查清单
- [ ] SMS认证流程测试
- [ ] 微信认证流程测试
- [ ] 设备验证流程测试
- [ ] 向后兼容性测试
- [ ] 错误处理测试
- [ ] 性能测试

### 6. 常见问题

#### Q: 迁移后原有的API还能正常工作吗？
A: 如果实现了向后兼容端点，原有API可以继续工作。建议逐步迁移到新的统一接口。

#### Q: 如何处理现有的前端缓存数据？
A: 建议在迁移时清理相关缓存，或者在代码中添加数据格式检查和转换逻辑。

#### Q: 统一格式会影响性能吗？
A: 适配器转换的性能开销很小，主要是对象字段的映射，不会显著影响性能。

#### Q: 如何确保迁移的正确性？
A: 建议先在测试环境进行完整迁移测试，确保所有功能正常后再部署到生产环境。

### 7. 迁移时间表建议

1. **第1周**: 后端统一认证模块开发和测试
2. **第2周**: 后端接口迁移和向后兼容实现
3. **第3周**: 前端类型定义和处理逻辑更新
4. **第4周**: 前端API调用迁移和测试
5. **第5周**: 集成测试和性能测试
6. **第6周**: 生产环境部署和监控

通过遵循这个迁移指南，您可以平滑地将现有系统迁移到统一认证返回值系统，提高代码的一致性和维护性。