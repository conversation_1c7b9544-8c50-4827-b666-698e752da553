# 微信认证模块部署指南

## 概述

本指南详细说明了如何部署和配置微信认证模块，包括微信公众号配置、服务器配置、数据库设置等。

## 前置条件

### 1. 微信公众号

- 已认证的微信公众号或服务号
- 获取AppID和AppSecret
- 配置服务器URL和Token

### 2. 服务器环境

- Python 3.8+
- FastAPI
- SQLAlchemy
- Redis（用于缓存和会话管理）
- PostgreSQL/MySQL（数据库）

## 微信公众号配置

### 1. 获取开发者凭据

1. 登录微信公众平台 (https://mp.weixin.qq.com/)
2. 进入「开发」->「基本配置」
3. 记录AppID和AppSecret

### 2. 配置服务器

1. 在「基本配置」中设置服务器URL:
   ```
   https://your-domain.com/api/v1/wechat/callback
   ```

2. 设置Token（自定义字符串）

3. 选择消息加解密方式（建议选择安全模式）

### 3. 配置网页授权域名

1. 进入「设置与开发」->「公众号设置」->「功能设置」
2. 设置网页授权域名:
   ```
   your-domain.com
   ```

### 4. 配置JS接口安全域名

1. 在「公众号设置」->「功能设置」中
2. 设置JS接口安全域名:
   ```
   your-domain.com
   ```

## 服务器配置

### 1. 环境变量配置

创建 `.env` 文件:

```bash
# 微信公众号配置
WECHAT_APP_ID=your_app_id_here
WECHAT_APP_SECRET=your_app_secret_here
WECHAT_TOKEN=your_token_here
WECHAT_ENCODING_AES_KEY=your_aes_key_here

# 微信API配置
WECHAT_DEBUG_MODE=false

# Redis配置（用于二维码状态存储）
REDIS_URL=redis://localhost:6379/0

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/dbname

# JWT配置
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 短信服务配置
SMS_ACCESS_KEY_ID=your_sms_access_key
SMS_ACCESS_KEY_SECRET=your_sms_secret
SMS_SIGN_NAME=your_sms_sign
SMS_TEMPLATE_CODE=your_template_code
```

### 2. 依赖安装

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用poetry
poetry install
```

### 3. 数据库迁移

```bash
# 创建数据库表
alembic upgrade head

# 或手动执行SQL
psql -d your_database -f migrations/create_tables.sql
```

### 4. Redis配置

```bash
# 启动Redis服务
sudo systemctl start redis

# 设置Redis开机自启
sudo systemctl enable redis

# 验证Redis连接
redis-cli ping
```

## 数据库表结构

### User表字段说明

确保User表包含以下字段:

```sql
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_openid VARCHAR(255) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_unionid VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_nickname VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS wechat_avatar TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS login_type VARCHAR(50) DEFAULT 'password';
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_wechat_openid ON users(wechat_openid);
CREATE INDEX IF NOT EXISTS idx_users_wechat_unionid ON users(wechat_unionid);
```

## 应用部署

### 1. 使用Docker部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

创建 `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/steam_app
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    env_file:
      - .env

  db:
    image: postgres:13
    environment:
      POSTGRES_DB: steam_app
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

部署命令:

```bash
# 构建和启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

### 2. 使用Systemd部署

创建服务文件 `/etc/systemd/system/steam-app.service`:

```ini
[Unit]
Description=Steam Aggregation Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/steam-app
Environment=PATH=/opt/steam-app/venv/bin
ExecStart=/opt/steam-app/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务:

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start steam-app

# 设置开机自启
sudo systemctl enable steam-app

# 查看状态
sudo systemctl status steam-app
```

### 3. Nginx反向代理配置

创建 `/etc/nginx/sites-available/steam-app`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 反向代理配置
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 静态文件配置
    location /static/ {
        alias /opt/steam-app/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}
```

启用站点:

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/steam-app /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载Nginx
sudo systemctl reload nginx
```

## 监控和日志

### 1. 日志配置

在 `app/core/logging.py` 中配置日志:

```python
import logging
from logging.handlers import RotatingFileHandler
import os

def setup_logging():
    # 创建日志目录
    log_dir = "/var/log/steam-app"
    os.makedirs(log_dir, exist_ok=True)
    
    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            RotatingFileHandler(
                f"{log_dir}/app.log",
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            ),
            logging.StreamHandler()
        ]
    )
    
    # 微信相关日志
    wechat_logger = logging.getLogger('wechat')
    wechat_handler = RotatingFileHandler(
        f"{log_dir}/wechat.log",
        maxBytes=10*1024*1024,
        backupCount=5
    )
    wechat_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    )
    wechat_logger.addHandler(wechat_handler)
```

### 2. 健康检查

添加健康检查端点:

```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health/wechat")
async def wechat_health_check():
    try:
        # 测试微信API连接
        wechat_service = WeChatService()
        token = await wechat_service.get_access_token()
        return {
            "status": "healthy" if token else "unhealthy",
            "service": "wechat",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "wechat",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
```

### 3. 监控脚本

创建监控脚本 `scripts/monitor.sh`:

```bash
#!/bin/bash

# 检查应用健康状态
check_health() {
    local url="https://your-domain.com/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" "$url")
    
    if [ "$response" = "200" ]; then
        echo "[$(date)] Application is healthy"
    else
        echo "[$(date)] Application health check failed: HTTP $response"
        # 发送告警
        send_alert "Application health check failed"
    fi
}

# 检查微信服务
check_wechat() {
    local url="https://your-domain.com/health/wechat"
    local response=$(curl -s "$url" | jq -r '.status')
    
    if [ "$response" = "healthy" ]; then
        echo "[$(date)] WeChat service is healthy"
    else
        echo "[$(date)] WeChat service is unhealthy"
        send_alert "WeChat service is unhealthy"
    fi
}

# 发送告警
send_alert() {
    local message="$1"
    # 这里可以集成钉钉、企业微信等告警方式
    echo "ALERT: $message" | mail -s "Steam App Alert" <EMAIL>
}

# 主循环
while true; do
    check_health
    check_wechat
    sleep 300  # 5分钟检查一次
done
```

## 安全配置

### 1. 防火墙配置

```bash
# 只开放必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 2. SSL证书配置

使用Let's Encrypt获取免费SSL证书:

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER steam_app WITH PASSWORD 'strong_password';
GRANT CONNECT ON DATABASE steam_db TO steam_app;
GRANT USAGE ON SCHEMA public TO steam_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO steam_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO steam_app;
```

## 故障排除

### 常见问题

1. **微信API调用失败**
   - 检查AppID和AppSecret是否正确
   - 确认服务器IP是否在微信白名单中
   - 检查网络连接和防火墙设置

2. **二维码无法显示**
   - 检查Redis连接是否正常
   - 确认二维码URL是否可访问
   - 检查微信公众号配置

3. **用户绑定失败**
   - 检查短信验证码服务
   - 确认数据库连接正常
   - 检查用户表结构是否正确

### 日志分析

```bash
# 查看应用日志
tail -f /var/log/steam-app/app.log

# 查看微信相关日志
tail -f /var/log/steam-app/wechat.log

# 查看错误日志
grep -i error /var/log/steam-app/app.log

# 查看特定用户的操作日志
grep "username:13800138000" /var/log/steam-app/app.log
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建必要的索引
CREATE INDEX CONCURRENTLY idx_users_wechat_openid ON users(wechat_openid) WHERE wechat_openid IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_users_username ON users(username);
CREATE INDEX CONCURRENTLY idx_users_last_login ON users(last_login);

-- 分析表统计信息
ANALYZE users;
```

### 2. Redis缓存优化

```python
# 配置Redis连接池
REDIS_POOL_CONFIG = {
    'max_connections': 20,
    'retry_on_timeout': True,
    'socket_timeout': 5,
    'socket_connect_timeout': 5
}
```

### 3. 应用优化

```python
# 使用连接池
from sqlalchemy.pool import QueuePool

engine = create_async_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

## 备份和恢复

### 数据库备份

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/database"
DB_NAME="steam_db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump -h localhost -U postgres $DB_NAME | gzip > $BACKUP_DIR/steam_db_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Database backup completed: steam_db_$DATE.sql.gz"
```

### 配置文件备份

```bash
# 备份配置文件
tar -czf /backup/config_$(date +%Y%m%d).tar.gz \
    /opt/steam-app/.env \
    /etc/nginx/sites-available/steam-app \
    /etc/systemd/system/steam-app.service
```

这个部署指南涵盖了微信认证模块的完整部署流程，包括环境配置、安全设置、监控和故障排除等方面。