# 微信认证API文档

## 概述

微信认证模块提供了完整的微信登录、绑定、解绑功能，支持二维码扫码登录和手机号绑定两种方式。

## API 接口

### 基础路径

所有微信认证相关的API都在 `/api/v1/wechat` 路径下。

### 1. 创建登录二维码

**接口**: `POST /wechat/qr-login/create`

**描述**: 创建微信扫码登录二维码

**请求参数**: 无

**响应示例**:
```json
{
  "scene_str": "login_1234567890",
  "qr_url": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxx",
  "expire_seconds": 600
}
```

### 2. 获取登录状态

**接口**: `GET /wechat/qr-login/status/{scene_str}`

**描述**: 获取微信扫码登录状态

**路径参数**:
- `scene_str`: 二维码场景字符串

**响应状态**:
- `waiting`: 等待扫码
- `scanned`: 已扫码，等待确认
- `confirmed`: 已确认登录
- `expired`: 二维码已过期
- `need_register`: 需要注册（用户未绑定）

**响应示例**:
```json
{
  "status": "confirmed",
  "message": "登录成功",
  "user_info": {
    "id": 1,
    "username": "13800138000",
    "nickname": "用户昵称"
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### 3. 微信账号绑定

**接口**: `POST /wechat/bind`

**描述**: 将微信账号与用户名（手机号）进行绑定

**请求参数**:
```json
{
  "openid": "wx_openid_123456",
  "username": "13800138000",
  "code": "123456"
}
```

**参数说明**:
- `openid`: 微信OpenID（必填）
- `username`: 用户名/手机号（必填，11位）
- `code`: 短信验证码（必填，4-6位）

**响应示例**:
```json
{
  "message": "微信账号绑定成功",
  "user": {
    "id": 1,
    "username": "13800138000",
    "nickname": "用户昵称",
    "wechat_nickname": "微信昵称",
    "avatar": "https://wx.qlogo.cn/mmopen/xxx"
  },
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer"
}
```

### 4. 解除微信绑定

**接口**: `POST /wechat/unbind`

**描述**: 解除当前用户的微信绑定

**请求头**: 需要Bearer Token认证

**请求参数**: 无

**响应示例**:
```json
{
  "message": "微信账号解绑成功",
  "user": {
    "id": 1,
    "username": "13800138000",
    "nickname": "用户昵称",
    "wechat_nickname": null,
    "avatar": null
  }
}
```

### 5. 获取微信绑定信息

**接口**: `GET /wechat/info`

**描述**: 获取当前用户的微信绑定信息

**请求头**: 需要Bearer Token认证

**响应示例**:
```json
{
  "is_bound": true,
  "wechat_nickname": "微信昵称",
  "wechat_avatar": "https://wx.qlogo.cn/mmopen/xxx",
  "login_type": "wechat"
}
```

## 绑定场景说明

### 场景1: 新用户注册
- OpenID和用户名都不存在
- 系统创建新用户并绑定微信
- 返回: "用户创建并绑定微信成功"

### 场景2: 现有用户绑定微信
- 用户名存在但未绑定微信
- 系统更新用户的微信信息
- 返回: "微信账号绑定成功"

### 场景3: 微信信息更新
- OpenID和用户名都已存在且匹配
- 系统更新微信用户信息
- 返回: "微信账号绑定成功"

### 场景4: 冲突检测
- OpenID已绑定其他用户名: 返回错误 "该微信账号已绑定其他用户"
- 用户名已绑定其他OpenID: 返回错误 "该用户名已绑定其他微信账号"

## 错误码说明

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| OPENID_ALREADY_BOUND | 400 | 该微信账号已绑定其他用户 |
| USERNAME_ALREADY_BOUND | 400 | 该用户名已绑定其他微信账号 |
| INVALID_VERIFICATION_CODE | 400 | 验证码无效或已过期 |
| WECHAT_NOT_BOUND | 400 | 该用户未绑定微信账号 |
| WECHAT_API_ERROR | 502 | 微信API调用失败 |
| QR_CODE_EXPIRED | 400 | 二维码已过期 |
| TOO_MANY_ATTEMPTS | 400 | 操作过于频繁，请稍后再试 |

## 安全特性

### 1. 验证码验证
- 所有绑定操作都需要短信验证码
- 验证码有效期和尝试次数限制

### 2. 冲突检测
- 防止一个微信账号绑定多个用户
- 防止一个用户绑定多个微信账号

### 3. 频率限制
- 绑定操作有频率限制
- 防止恶意攻击

### 4. 数据库事务
- 所有操作都在事务中执行
- 确保数据一致性

## 配置说明

### 环境变量

```bash
# 微信公众号配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 调试模式
WECHAT_DEBUG_MODE=false
```

### 配置参数

- `qr_expire_seconds`: 二维码过期时间（默认600秒）
- `max_bind_attempts`: 最大绑定尝试次数（默认5次）
- `bind_attempt_window`: 绑定尝试时间窗口（默认3600秒）

## 使用示例

### 前端集成示例

```javascript
// 1. 创建登录二维码
const createQRCode = async () => {
  const response = await fetch('/api/v1/wechat/qr-login/create', {
    method: 'POST'
  });
  const data = await response.json();
  // 显示二维码
  document.getElementById('qr-image').src = data.qr_url;
  // 开始轮询状态
  pollLoginStatus(data.scene_str);
};

// 2. 轮询登录状态
const pollLoginStatus = async (sceneStr) => {
  const response = await fetch(`/api/v1/wechat/qr-login/status/${sceneStr}`);
  const data = await response.json();
  
  if (data.status === 'confirmed') {
    // 登录成功，保存token
    localStorage.setItem('access_token', data.access_token);
    window.location.href = '/dashboard';
  } else if (data.status === 'need_register') {
    // 需要注册，跳转到绑定页面
    window.location.href = `/bind?openid=${data.user_info.openid}`;
  } else if (data.status !== 'expired') {
    // 继续轮询
    setTimeout(() => pollLoginStatus(sceneStr), 2000);
  }
};

// 3. 绑定微信账号
const bindWechat = async (openid, username, code) => {
  const response = await fetch('/api/v1/wechat/bind', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ openid, username, code })
  });
  
  if (response.ok) {
    const data = await response.json();
    localStorage.setItem('access_token', data.access_token);
    alert(data.message);
  } else {
    const error = await response.json();
    alert(error.detail);
  }
};
```

## 注意事项

1. **验证码验证**: 所有绑定操作都需要先发送短信验证码
2. **唯一性约束**: 确保OpenID和用户名的一对一关系
3. **错误处理**: 前端需要处理各种错误情况
4. **Token管理**: 登录成功后需要妥善保存和使用access_token
5. **安全考虑**: 不要在前端暴露敏感的微信配置信息

## 测试

项目包含完整的测试用例，位于 `test_wechat_bind.py` 文件中，涵盖了所有主要场景的测试。

运行测试:
```bash
pytest test_wechat_bind.py -v
```