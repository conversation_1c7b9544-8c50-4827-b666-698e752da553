# 微信认证模块

## 概述

本模块提供了完整的微信认证解决方案，包括二维码登录、账号绑定、信息管理等功能。采用模块化设计，具有高性能、高可用性和良好的可扩展性。

## 🚀 功能特性

### 核心功能
- ✅ **二维码登录** - 支持微信扫码登录
- ✅ **账号绑定** - 智能绑定微信账号与手机号
- ✅ **信息管理** - 获取和管理微信绑定信息
- ✅ **账号解绑** - 安全解除微信绑定
- ✅ **多场景处理** - 支持5种不同的绑定场景

### 技术特性
- 🔒 **安全性** - 验证码验证、数据库事务保护
- 🚀 **高性能** - 缓存优化、连接池管理
- 📊 **监控** - 完整的性能监控和健康检查
- 🛡️ **限流** - 防止恶意请求和暴力破解
- 🔧 **可配置** - 灵活的配置管理

## 📁 项目结构

```
app/
├── api/endpoints/
│   └── wechat_auth.py          # 微信认证路由
├── core/
│   ├── wechat_config.py        # 微信配置
│   ├── wechat_exceptions.py    # 微信异常定义
│   └── wechat_monitoring.py    # 监控和性能优化
├── schemas/
│   └── wechat.py              # 微信相关数据模型
└── services/
    └── wechat_service.py      # 微信服务（需要实现）

docs/
├── wechat_auth_api.md         # API文档
└── wechat_deployment_guide.md # 部署指南

examples/
└── wechat_auth_example.py     # 使用示例

tests/
└── test_wechat_bind.py        # 测试用例
```

## 🔧 快速开始

### 1. 环境配置

在 `.env` 文件中添加微信配置：

```bash
# 微信公众号配置
WECHAT_APP_ID=your_app_id
WECHAT_APP_SECRET=your_app_secret

# 微信API配置
WECHAT_API_BASE_URL=https://api.weixin.qq.com
WECHAT_QR_EXPIRE_SECONDS=600

# Redis配置（用于二维码状态存储）
REDIS_URL=redis://localhost:6379/0
WECHAT_QR_REDIS_PREFIX=wechat:qr:

# 安全配置
WECHAT_MAX_BIND_ATTEMPTS=5
WECHAT_BIND_TIME_WINDOW=3600

# 调试模式
WECHAT_DEBUG_MODE=false
```

### 2. 数据库迁移

确保 `User` 表包含以下字段：

```sql
ALTER TABLE users ADD COLUMN wechat_openid VARCHAR(128) UNIQUE;
ALTER TABLE users ADD COLUMN wechat_nickname VARCHAR(255);
ALTER TABLE users ADD COLUMN wechat_avatar TEXT;
ALTER TABLE users ADD COLUMN login_type VARCHAR(20) DEFAULT 'phone';
```

### 3. 启动服务

```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 📚 API 接口

### 基础路径
所有微信认证接口的基础路径为：`/api/v1/wechat`

### 接口列表

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/qr-login/create` | 创建登录二维码 |
| GET | `/qr-login/status/{scene_str}` | 获取登录状态 |
| POST | `/bind` | 绑定微信账号 |
| POST | `/unbind` | 解除微信绑定 |
| GET | `/info` | 获取微信信息 |

### 使用示例

#### 1. 创建二维码

```bash
curl -X POST "http://localhost:8000/api/v1/wechat/qr-login/create" \
  -H "Content-Type: application/json"
```

响应：
```json
{
  "scene_str": "login_123456",
  "qr_url": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxx",
  "expire_seconds": 600
}
```

#### 2. 绑定微信账号

```bash
curl -X POST "http://localhost:8000/api/v1/wechat/bind" \
  -H "Content-Type: application/json" \
  -d '{
    "openid": "your_openid",
    "username": "13800138000",
    "code": "123456"
  }'
```

## 🔍 绑定场景

系统支持以下5种绑定场景：

1. **OpenID已绑定其他用户名** - 返回错误，不允许重复绑定
2. **用户名已绑定其他OpenID** - 返回错误，需要先解绑
3. **用户名存在但未绑定微信** - 直接绑定到现有用户
4. **OpenID和用户名都已存在且匹配** - 返回成功，更新登录信息
5. **都不存在** - 创建新用户并绑定

## 🛠️ 开发指南

### 添加新的微信服务

1. 在 `app/services/wechat_service.py` 中实现微信API调用
2. 在 `app/api/endpoints/wechat_auth.py` 中添加新的路由
3. 在 `app/schemas/wechat.py` 中定义数据模型
4. 添加相应的测试用例

### 自定义异常处理

```python
from app.core.wechat_exceptions import WeChatException

class CustomWeChatError(WeChatException):
    def __init__(self):
        super().__init__(
            status_code=400,
            detail="自定义错误信息",
            error_code="CUSTOM_ERROR"
        )
```

### 性能监控

使用装饰器添加监控：

```python
from app.core.wechat_monitoring import monitor_performance, rate_limit

@monitor_performance("custom_endpoint")
@rate_limit(max_requests=10)
async def custom_endpoint():
    # 你的代码
    pass
```

## 📊 监控和运维

### 健康检查

```bash
curl "http://localhost:8000/health"
```

### 监控指标

```bash
curl "http://localhost:8000/metrics"
```

### 日志配置

日志文件位置：`logs/wechat_auth.log`

日志级别配置：
```python
# 在 app/core/logger.py 中配置
logging.getLogger("app.api.endpoints.wechat_auth").setLevel(logging.INFO)
```

## 🧪 测试

### 运行单元测试

```bash
# 运行所有微信认证测试
python test_wechat_bind.py

# 运行集成测试
python test_wechat_bind.py integration
```

### 运行示例程序

```bash
python examples/wechat_auth_example.py
```

## 🔒 安全考虑

### 数据保护
- OpenID 和用户信息加密存储
- 敏感日志信息脱敏处理
- 定期清理过期的二维码数据

### 访问控制
- 验证码有效期限制
- 绑定尝试次数限制
- IP 级别的限流保护

### 审计日志
- 记录所有绑定和解绑操作
- 记录异常访问行为
- 定期安全审计

## 🚀 性能优化

### 缓存策略
- 微信用户信息缓存（5分钟）
- 二维码状态缓存（10分钟）
- 验证码状态缓存（1分钟）

### 数据库优化
- 为 `wechat_openid` 字段添加索引
- 使用连接池管理数据库连接
- 定期清理过期数据

### 并发处理
- 异步处理微信API调用
- 使用连接池管理HTTP请求
- 合理设置超时时间

## 📋 故障排除

### 常见问题

1. **二维码生成失败**
   - 检查微信公众号配置
   - 验证网络连接
   - 查看错误日志

2. **绑定失败**
   - 验证验证码是否正确
   - 检查用户是否已存在
   - 确认OpenID格式正确

3. **性能问题**
   - 检查数据库连接池配置
   - 监控缓存命中率
   - 查看响应时间指标

### 调试模式

启用调试模式：
```bash
export WECHAT_DEBUG_MODE=true
```

调试模式下会输出详细的日志信息，包括微信API调用详情。

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- ✅ 实现二维码登录功能
- ✅ 实现账号绑定功能
- ✅ 添加性能监控
- ✅ 完善文档和测试

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查 [Issues](https://github.com/your-repo/issues) 中是否有类似问题
3. 创建新的 Issue 描述您的问题

---

**注意**: 在生产环境中使用前，请确保已经完成所有安全配置和性能优化设置。