# 权限系统迁移指南

## 概述

本项目已完成权限系统的统一重构，从原来的两套权限系统（基于字符串的旧系统和基于枚举的新系统）整合为一套统一的权限系统。本指南将帮助开发者了解如何迁移现有代码。

## 新统一权限系统特性

### 1. 核心特性
- **统一权限模型**：基于资源类型、操作和范围的三维权限模型
- **权限缓存**：自动缓存权限检查结果，提高性能
- **条件权限**：支持基于条件的动态权限检查
- **权限过期**：支持临时权限和权限过期机制
- **审计日志**：完整的权限检查日志记录
- **向后兼容**：完全兼容旧的字符串权限格式

### 2. 权限模型

```python
# 权限对象结构
Permission(
    resource=ResourceType.ARTICLE,  # 资源类型
    action=Action.READ,             # 操作类型
    scope=Scope.PUBLIC,             # 权限范围
    conditions={},                  # 条件（可选）
    expires_at=None,               # 过期时间（可选）
    description=""                 # 描述（可选）
)
```

## 迁移步骤

### 第一阶段：兼容性迁移（已完成）

所有旧的权限检查代码无需修改，系统会自动将旧格式权限映射到新系统：

```python
# 旧代码（仍然有效）
check_permissions(["users:list", "users:create"])

# 自动映射到新权限
# "users:list" -> Permissions.USER_READ_ALL
# "users:create" -> Permissions.USER_CREATE
```

### 第二阶段：逐步迁移到新API

#### 1. 权限检查迁移

**旧方式：**
```python
from app.api.deps import check_permissions
from app.core.permission_system import PermissionChecker, Permissions

# 在路由中
@router.get("/users")
def get_users(current_user: User = Depends(check_permissions(["users:list"]))):
    pass

# 在服务中
if has_permission(user, "articles:update"):
    # 执行操作
    pass
```

**新方式：**
```python
from app.api.permission_deps import PermissionDeps
from app.core.permission_system import Permissions, PermissionChecker
from app.services.unified_permission_service import UnifiedPermissionService

# 在路由中 - 使用预定义权限
@router.get("/users")
def get_users(current_user: User = Depends(PermissionDeps.require_permission(Permissions.USER_READ_ALL))):
    pass

# 在路由中 - 使用字符串权限（兼容）
@router.get("/users")
def get_users(current_user: User = Depends(PermissionDeps.require_permission("users:list"))):
    pass

# 在服务中
permission_service = UnifiedPermissionService(db)
if permission_service.check_permission(user, Permissions.ARTICLE_UPDATE_OWN):
    # 执行操作
    pass
```

#### 2. 资源级权限检查

**旧方式：**
```python
# 文章权限检查
from app.services.article_permission_service import ArticlePermissionService

article_service = ArticlePermissionService(db)
if article_service.check_article_update_permission(user, article):
    # 更新文章
    pass
```

**新方式：**
```python
# 使用统一权限服务
from app.services.unified_permission_service import UnifiedPermissionService
from app.core.permission_system import Action

permission_service = UnifiedPermissionService(db)
if permission_service.check_article_access(user, article, Action.UPDATE):
    # 更新文章
    pass

# 或者使用权限检查器
from app.core.permission_system import PermissionChecker, Permission, ResourceType, Action, Scope

permission = Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.OWN)
if PermissionChecker.check_permission(user, permission, article):
    # 更新文章
    pass
```

#### 3. 批量权限检查

**旧方式：**
```python
# 获取可访问文章
from app.services.article_permission_service import ArticlePermissionService

article_service = ArticlePermissionService(db)
accessible_articles = article_service.get_accessible_articles_by_category(user, category_id)
```

**新方式：**
```python
# 使用统一权限服务
from app.services.unified_permission_service import UnifiedPermissionService

permission_service = UnifiedPermissionService(db)
accessible_articles = permission_service.get_accessible_articles_by_category(user, category_id)
```

## 新功能使用指南

### 1. 条件权限

```python
from app.core.permission_system import Permission, ResourceType, Action, Scope
from datetime import datetime, timedelta

# 创建带条件的权限
permission = Permission(
    resource=ResourceType.ARTICLE,
    action=Action.UPDATE,
    scope=Scope.OWN,
    conditions={"status": "draft"},  # 只能编辑草稿状态的文章
    expires_at=datetime.now() + timedelta(hours=24)  # 24小时后过期
)

# 检查权限
if PermissionChecker.check_permission(user, permission, article):
    # 执行操作
    pass
```

### 2. 权限缓存管理

```python
from app.core.permission_system import clear_user_permissions_cache, clear_all_permissions_cache
from app.services.unified_permission_service import UnifiedPermissionService

# 清除特定用户的权限缓存
clear_user_permissions_cache(user_id)

# 清除所有权限缓存
clear_all_permissions_cache()

# 通过服务清除缓存
permission_service = UnifiedPermissionService(db)
permission_service.clear_user_cache(user_id)
```

### 3. 权限配置

```python
from app.core.permission_system import update_permission_config

# 更新权限配置
update_permission_config(
    cache_enabled=True,
    cache_ttl=600,  # 10分钟
    strict_mode=True,
    audit_enabled=True
)
```

### 4. 权限摘要

```python
from app.services.unified_permission_service import UnifiedPermissionService

permission_service = UnifiedPermissionService(db)
summary = permission_service.get_user_permissions_summary(user)

# 返回结果示例
# {
#     "role": "admin",
#     "permissions": ["USER:READ:ALL", "ARTICLE:MANAGE:ALL", ...],
#     "can_create_article": True,
#     "can_create_video": True,
#     "can_manage_users": True,
#     "can_manage_system": False
# }
```

## 最佳实践

### 1. 权限检查优先级

1. **路由级权限**：在FastAPI路由中使用依赖注入进行权限检查
2. **服务级权限**：在业务逻辑中进行细粒度权限检查
3. **资源级权限**：对特定资源进行所有权和状态检查

### 2. 权限设计原则

- **最小权限原则**：用户只拥有完成任务所需的最小权限
- **权限分离**：不同类型的操作使用不同的权限
- **资源隔离**：确保用户只能访问授权的资源

### 3. 性能优化

- **启用缓存**：对频繁的权限检查启用缓存
- **批量检查**：使用批量权限检查方法减少数据库查询
- **合理的缓存TTL**：根据权限变更频率设置合适的缓存时间

## 常见问题

### Q1: 旧代码是否需要立即修改？
A: 不需要。新系统完全兼容旧的权限格式，现有代码可以继续正常工作。

### Q2: 如何处理自定义权限？
A: 可以通过扩展ResourceType和Action枚举来添加新的权限类型，或者使用条件权限实现复杂的权限逻辑。

### Q3: 权限缓存何时会失效？
A: 权限缓存会在TTL到期、用户权限变更或手动清除时失效。

### Q4: 如何调试权限问题？
A: 启用audit_enabled配置，查看权限检查日志；使用get_user_permissions_summary获取用户权限摘要。

## 迁移检查清单

- [ ] 确认所有旧权限检查仍然正常工作
- [ ] 逐步将关键路由迁移到新的权限依赖
- [ ] 更新服务层使用统一权限服务
- [ ] 配置权限缓存和审计
- [ ] 添加权限相关的单元测试
- [ ] 更新API文档中的权限说明

## 后续计划

1. **第三阶段**：完全迁移到新API，移除旧的权限检查代码
2. **第四阶段**：添加更多高级功能（权限继承、动态权限等）
3. **第五阶段**：权限管理界面开发

---

如有任何问题，请参考 `权限系统重构方案.md` 或联系开发团队。