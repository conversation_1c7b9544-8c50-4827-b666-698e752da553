# 文章状态查询功能重构文档

## 概述

本次重构优化了文章状态查询功能，提供了更高效、类型安全的文章筛选机制。

## 主要改进

### 1. 添加文章状态枚举 (`ArticleStatus`)

**文件**: `app/schemas/article.py`

```python
class ArticleStatus(str, Enum):
    """文章状态枚举"""
    ALL = "all"  # 所有文章
    DRAFT = "draft"  # 草稿（未发布）
    PUBLISHED_APPROVED = "published_approved"  # 已发布且已审核通过
    PUBLISHED_PENDING = "published_pending"  # 已发布但待审核
    PUBLISHED_REJECTED = "published_rejected"  # 已发布但审核被拒绝
```

**优势**:
- 提供类型安全的状态管理
- 避免硬编码字符串
- 支持IDE自动补全
- 便于维护和扩展

### 2. 优化CRUD查询方法

**文件**: `app/crud/article.py`

新增 `get_multi_by_author_with_status` 方法：

```python
async def get_multi_by_author_with_status(
    self, 
    db: AsyncSession, 
    *, 
    author_id: int, 
    status: ArticleStatus = ArticleStatus.ALL,
    skip: int = 0, 
    limit: int = 100
) -> Tuple[list[Article], int]:
    """根据状态获取指定作者的文章列表和总数（优化版本）"""
```

**优化点**:
- 一次查询同时获取文章列表和总数
- 减少数据库访问次数
- 支持多种状态筛选
- 添加排序（按更新时间倒序）

### 3. 简化API端点

**文件**: `app/api/endpoints/articles.py`

更新 `/my` 端点：

```python
@router.get("/my", response_model=schemas.ArticleListWithStats)
async def read_my_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: schemas.ArticleStatus = Query(schemas.ArticleStatus.ALL, description="文章状态筛选"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
```

**改进**:
- 添加 `status` 参数支持状态筛选
- 使用枚举类型确保参数有效性
- 提供详细的API文档说明
- 保持向后兼容性

## 使用方法

### API调用示例

```bash
# 获取所有文章
GET /api/articles/my?status=all

# 获取草稿文章
GET /api/articles/my?status=draft

# 获取已发布且已审核的文章
GET /api/articles/my?status=published_approved

# 获取已发布但待审核的文章
GET /api/articles/my?status=published_pending

# 获取已发布但被拒绝的文章
GET /api/articles/my?status=published_rejected

# 分页查询
GET /api/articles/my?status=draft&skip=0&limit=10
```

### 状态映射关系

| 状态枚举 | 数据库条件 | 说明 |
|---------|-----------|------|
| `ALL` | 无额外条件 | 所有文章 |
| `DRAFT` | `is_published = False` | 草稿文章 |
| `PUBLISHED_APPROVED` | `is_published = True AND is_approved = True` | 已发布且已审核 |
| `PUBLISHED_PENDING` | `is_published = True AND is_approved = False` | 已发布但待审核 |
| `PUBLISHED_REJECTED` | `is_published = True AND is_approved = False` | 已发布但被拒绝 |

## 性能优化

### 查询优化

1. **减少数据库访问**: 从原来的2次查询（列表+总数）优化为1次查询
2. **索引建议**: 为提高查询性能，建议添加复合索引：
   ```sql
   CREATE INDEX idx_article_author_status ON articles(author_id, is_published, is_approved);
   ```

### 内存优化

1. **预加载关联数据**: 使用 `selectinload` 预加载标签和作者信息
2. **分页查询**: 支持 `skip` 和 `limit` 参数控制返回数据量

## 向后兼容性

- 原有的API调用方式仍然有效（默认返回所有文章）
- 现有的CRUD方法保持不变
- 新功能为可选参数，不影响现有代码

## 扩展性

### 添加新状态

如需添加新的文章状态，只需：

1. 在 `ArticleStatus` 枚举中添加新状态
2. 在 `get_multi_by_author_with_status` 方法中添加对应的查询条件
3. 更新API文档

### 缓存支持

未来可以轻松添加Redis缓存支持：

```python
# 示例：添加缓存装饰器
@cache_result(ttl=300)  # 缓存5分钟
async def get_multi_by_author_with_status(...):
    # 现有实现
```

## 测试

运行测试脚本验证功能：

```bash
python3 test_article_status.py
```

## 总结

本次重构实现了以下目标：

✅ **性能提升**: 减少数据库查询次数，提高响应速度  
✅ **类型安全**: 使用枚举避免字符串错误  
✅ **代码质量**: 提高可读性和维护性  
✅ **用户体验**: 提供灵活的文章状态筛选功能  
✅ **向后兼容**: 不影响现有功能  
✅ **扩展性**: 便于未来添加新功能  

这次重构为文章管理系统提供了更强大、更高效的状态查询能力，为用户提供了更好的内容管理体验。