# 统一认证返回值设计文档

## 概述

为了统一SMS认证和微信认证的返回值结构，我们设计了一套统一的认证响应模型。这样可以确保前端在处理不同认证方式的响应时具有一致的数据结构。

## 核心组件

### 1. 统一用户信息模型 (UnifiedUserInfo)

```python
class UnifiedUserInfo(BaseModel):
    id: int                                    # 用户ID
    username: str                              # 用户名
    nickname: Optional[str] = None             # 昵称
    email: Optional[str] = None                # 邮箱
    avatar: Optional[str] = None               # 头像URL
    is_active: bool                            # 是否激活
    last_login: Optional[datetime] = None      # 最后登录时间
    created_at: datetime                       # 创建时间
    
    # 微信相关字段
    wechat_nickname: Optional[str] = None      # 微信昵称
    wechat_avatar: Optional[str] = None        # 微信头像
    login_type: Optional[str] = None           # 登录方式
```

### 2. 统一认证响应模型 (UnifiedAuthResponse)

```python
class UnifiedAuthResponse(BaseModel):
    access_token: str                          # 访问令牌
    token_type: str = "bearer"                 # 令牌类型
    user: UnifiedUserInfo                      # 用户信息
    message: Optional[str] = None              # 操作结果消息
    auth_method: str                           # 认证方式: sms, wechat
```

### 3. 统一设备验证响应模型 (UnifiedDeviceVerificationResponse)

```python
class UnifiedDeviceVerificationResponse(BaseModel):
    requires_device_verification: bool = True  # 需要设备验证
    message: str                               # 验证消息
    device_info: Optional[Dict[str, Any]] = None  # 设备信息
    verification_token: Optional[str] = None   # 验证令牌
    auth_method: str                           # 认证方式: sms, wechat
```

## API 接口变更

### SMS 认证接口

#### 1. 验证登录/注册验证码

**接口**: `POST /api/sms-auth/verify-auth-code`

**返回值**: `UnifiedAuthResponse | UnifiedDeviceVerificationResponse`

**示例响应**:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "13800138000",
    "nickname": "用户昵称",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "is_active": true,
    "last_login": "2024-01-01T12:00:00",
    "created_at": "2024-01-01T10:00:00",
    "wechat_nickname": null,
    "wechat_avatar": null,
    "login_type": "sms"
  },
  "message": "登录成功",
  "auth_method": "sms"
}
```

#### 2. 验证设备验证码

**接口**: `POST /api/sms-auth/verify-device-code`

**返回值**: `UnifiedAuthResponse`

### 微信认证接口

#### 1. 获取登录状态

**接口**: `GET /api/wechat-auth/qr-login/status/{scene_str}`

**返回值**: `UnifiedAuthResponse | LoginStatusResponse`

**示例响应**:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": 2,
    "username": "13900139000",
    "nickname": "微信用户",
    "email": null,
    "avatar": "https://wx.qlogo.cn/mmopen/...",
    "is_active": true,
    "last_login": "2024-01-01T12:00:00",
    "created_at": "2024-01-01T10:00:00",
    "wechat_nickname": "微信昵称",
    "wechat_avatar": "https://wx.qlogo.cn/mmopen/...",
    "login_type": "wechat"
  },
  "message": "登录成功",
  "auth_method": "wechat"
}
```

#### 2. 微信账号绑定

**接口**: `POST /api/wechat-auth/bind`

**返回值**: `UnifiedAuthResponse`

## 适配器使用

### UnifiedAuthAdapter

适配器类提供了多种转换方法：

```python
from app.services.unified_auth_adapter import unified_auth_adapter

# 从SMS认证结果转换
unified_result = unified_auth_adapter.from_sms_auth_result(sms_result)
response = unified_result.to_auth_response()

# 从微信绑定响应转换
unified_response = unified_auth_adapter.from_wechat_bind_response(wechat_response)

# 向后兼容转换
sms_response = unified_auth_adapter.to_legacy_sms_response(unified_response)
wechat_response = unified_auth_adapter.to_legacy_wechat_response(unified_response)
```

## 前端使用指南

### 统一处理认证响应

```typescript
interface UnifiedAuthResponse {
  access_token: string;
  token_type: string;
  user: {
    id: number;
    username: string;
    nickname?: string;
    email?: string;
    avatar?: string;
    is_active: boolean;
    last_login?: string;
    created_at: string;
    wechat_nickname?: string;
    wechat_avatar?: string;
    login_type?: string;
  };
  message?: string;
  auth_method: 'sms' | 'wechat';
}

// 统一处理函数
function handleAuthSuccess(response: UnifiedAuthResponse) {
  // 保存token
  localStorage.setItem('access_token', response.access_token);
  
  // 保存用户信息
  const userInfo = {
    ...response.user,
    // 根据认证方式选择合适的头像
    avatar: response.user.wechat_avatar || response.user.avatar,
    // 根据认证方式选择合适的昵称
    displayName: response.user.wechat_nickname || response.user.nickname || response.user.username
  };
  
  localStorage.setItem('user_info', JSON.stringify(userInfo));
  
  // 显示成功消息
  if (response.message) {
    showSuccessMessage(response.message);
  }
  
  // 根据认证方式执行特定逻辑
  if (response.auth_method === 'wechat') {
    // 微信登录特定逻辑
    trackEvent('wechat_login_success');
  } else if (response.auth_method === 'sms') {
    // SMS登录特定逻辑
    trackEvent('sms_login_success');
  }
}
```

### 设备验证处理

```typescript
interface UnifiedDeviceVerificationResponse {
  requires_device_verification: boolean;
  message: string;
  device_info?: any;
  verification_token?: string;
  auth_method: 'sms' | 'wechat';
}

function handleDeviceVerification(response: UnifiedDeviceVerificationResponse) {
  // 显示设备验证界面
  showDeviceVerificationModal({
    message: response.message,
    deviceInfo: response.device_info,
    verificationToken: response.verification_token,
    authMethod: response.auth_method
  });
}
```

## 迁移指南

### 现有代码迁移

1. **更新API响应处理**：将现有的SMS和微信认证响应处理代码统一为处理`UnifiedAuthResponse`

2. **更新类型定义**：使用新的统一类型定义替换原有的分散类型

3. **向后兼容**：如果需要保持向后兼容，可以使用适配器的转换方法

### 测试更新

更新相关测试用例以验证新的统一响应格式：

```python
def test_unified_sms_auth_response():
    # 测试SMS认证返回统一格式
    response = client.post("/api/sms-auth/verify-auth-code", json={
        "phone": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # 验证统一格式
    assert "access_token" in data
    assert "token_type" in data
    assert "user" in data
    assert "auth_method" in data
    assert data["auth_method"] == "sms"

def test_unified_wechat_auth_response():
    # 测试微信认证返回统一格式
    response = client.post("/api/wechat-auth/bind", json={
        "openid": "wx_openid_123",
        "username": "13800138000",
        "code": "123456"
    })
    
    assert response.status_code == 200
    data = response.json()
    
    # 验证统一格式
    assert "access_token" in data
    assert "token_type" in data
    assert "user" in data
    assert "auth_method" in data
    assert data["auth_method"] == "wechat"
```

## 优势

1. **一致性**：前端处理不同认证方式时具有统一的数据结构
2. **可扩展性**：易于添加新的认证方式
3. **向后兼容**：通过适配器保持与现有代码的兼容性
4. **类型安全**：提供完整的类型定义
5. **维护性**：减少重复代码，提高代码质量

## 注意事项

1. **字段映射**：确保所有必要字段都正确映射到统一模型中
2. **空值处理**：妥善处理可选字段的空值情况
3. **性能考虑**：适配器转换过程中避免不必要的数据复制
4. **错误处理**：确保转换过程中的错误能够被正确捕获和处理