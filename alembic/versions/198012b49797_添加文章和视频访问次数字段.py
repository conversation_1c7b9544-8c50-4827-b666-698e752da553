"""添加文章和视频访问次数字段

Revision ID: 198012b49797
Revises: a13e82222cd2
Create Date: 2025-06-30 03:15:43.933550

"""
from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "198012b49797"
down_revision: str | None = "a13e82222cd2"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("articles", sa.Column("visit_count", sa.Integer(), nullable=True, comment="访问次数"))
    op.add_column("history", sa.Column("last_visited_at", sa.DateTime(), nullable=True))
    op.add_column("history", sa.Column("visit_count", sa.Integer(), nullable=True))
    op.add_column("videos", sa.<PERSON>umn("visit_count", sa.Integer(), nullable=True, comment="访问次数"))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("videos", "visit_count")
    op.drop_column("history", "visit_count")
    op.drop_column("history", "last_visited_at")
    op.drop_column("articles", "visit_count")
    # ### end Alembic commands ###
